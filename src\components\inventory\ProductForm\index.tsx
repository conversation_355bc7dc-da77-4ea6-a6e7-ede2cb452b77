import React, { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Form } from '@/components/ui/form'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import {
  useActiveProductCategories,
  useCreateProduct,
  useUpdateProduct
} from '@/hooks/queries'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuthHook'
import { ProductWithStock } from '@/types/inventory'
import { ProductFormData } from '@/types/inventory'

// Import sub-components
import { ProductBasicInfo } from './ProductBasicInfo'
import { ProductPricing } from './ProductPricing'
import { ProductInventory } from './ProductInventory'
import { ProductPhysical } from './ProductPhysical'
import { ProductSettings } from './ProductSettings'

// Import types and utilities
import { productFormSchema, ProductFormValues } from './types'
import {
  generateSkuSuggestions,
  validatePricing,
  transformFormDataForSubmission,
  transformProductForForm,
} from './utils'

interface ProductFormProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  product?: ProductWithStock | null
  onSuccess?: () => void
}

export { ProductBasicInfo } from './ProductBasicInfo'
export { ProductPricing } from './ProductPricing'
export { ProductInventory } from './ProductInventory'
export { ProductPhysical } from './ProductPhysical'
export { ProductSettings } from './ProductSettings'
export * from './types'
export * from './utils'

export function ProductForm({ open, onOpenChange, product, onSuccess }: ProductFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [validationErrors, setValidationErrors] = useState<Record<string, string[]>>({})
  const [validationWarnings, setValidationWarnings] = useState<Record<string, string[]>>({})
  const [skuSuggestions, setSkuSuggestions] = useState<string[]>([])
  const [selectedImage, setSelectedImage] = useState<File | null>(null)
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [showPricingHelp, setShowPricingHelp] = useState(false)

  const { profile } = useAuth()
  const { data: categories = [] } = useActiveProductCategories()
  const createProduct = useCreateProduct()
  const updateProduct = useUpdateProduct()

  const form = useForm<ProductFormValues>({
    resolver: zodResolver(productFormSchema),
    defaultValues: {
      sku: '',
      name: '',
      description: '',
      category_id: '__none__',
      unit_of_measure: 'piece',
      cost_price: 0,
      selling_price: 0,
      track_inventory: true,
      initial_quantity: 0,
      reorder_level: 0,
      reorder_quantity: 0,
      barcode: '',
      weight: null,
      dimensions: '',
      is_active: true,
      is_sellable: true,
      is_purchasable: true,
    },
  })

  // Watch form values for reactive updates
  const trackInventory = form.watch('track_inventory')
  const costPrice = form.watch('cost_price')
  const sellingPrice = form.watch('selling_price')
  const productName = form.watch('name')
  const categoryId = form.watch('category_id')

  // Calculate margin percentage
  const marginPercentage = sellingPrice > 0 
    ? (((sellingPrice - costPrice) / sellingPrice) * 100).toFixed(1)
    : '0.0'

  // Reset form when product changes
  useEffect(() => {
    if (product) {
      const formData = transformProductForForm(product)
      form.reset(formData)
    } else {
      form.reset({
        sku: '',
        name: '',
        description: '',
        category_id: '__none__',
        unit_of_measure: 'piece',
        cost_price: 0,
        selling_price: 0,
        track_inventory: true,
        initial_quantity: 0,
        reorder_level: 0,
        reorder_quantity: 0,
        barcode: '',
        weight: null,
        dimensions: '',
        is_active: true,
        is_sellable: true,
        is_purchasable: true,
      })
    }
  }, [product, form])

  // Generate SKU suggestions when name or category changes
  useEffect(() => {
    if (productName && !product) {
      const suggestions = generateSkuSuggestions(productName, categoryId === '__none__' ? undefined : categoryId)
      setSkuSuggestions(suggestions)
    }
  }, [productName, categoryId, product])

  // Custom function to create product with initial stock
  const createProductWithInitialStock = async (formData: ProductFormData) => {
    if (!profile?.org_id) throw new Error('Organization ID is required')

    // Create the product first
    const createdProduct = await createProduct.mutateAsync(formData)

    // If initial quantity is provided and track_inventory is true, create stock level
    if (formData.initial_quantity && formData.initial_quantity > 0 && formData.track_inventory) {
      // Get default location
      const { data: defaultLocation } = await supabase
        .from('inventory_locations')
        .select('id')
        .eq('org_id', profile.org_id)
        .eq('is_default', true)
        .eq('is_active', true)
        .single()

      if (!defaultLocation) {
        console.warn('No default inventory location found. Skipping initial stock creation.')
        return createdProduct
      }

      // Create initial stock level entry
      const { error: stockError } = await supabase
        .from('stock_levels')
        .insert({
          org_id: profile.org_id,
          product_id: createdProduct.id,
          location_id: defaultLocation.id,
          quantity_on_hand: formData.initial_quantity,
          quantity_reserved: 0,
          average_cost: formData.cost_price || 0,
          last_cost: formData.cost_price || 0,
          last_updated: new Date().toISOString(),
        })

      if (stockError) {
        console.error('Failed to create initial stock level:', stockError)
      } else {
        // Create an inventory transaction for the initial stock
        const { error: transactionError } = await supabase
          .from('inventory_transactions')
          .insert({
            org_id: profile.org_id,
            product_id: createdProduct.id,
            location_id: defaultLocation.id,
            transaction_type: 'adjustment',
            quantity: formData.initial_quantity,
            unit_cost: formData.cost_price || 0,
            total_cost: (formData.initial_quantity * (formData.cost_price || 0)),
            reference_type: 'initial_stock',
            reference_number: `INIT-${createdProduct.sku}`,
            reason_code: 'initial_stock',
            notes: 'Initial stock entry for new product',
            created_by: profile.id,
          })

        if (transactionError) {
          console.error('Failed to create initial stock transaction:', transactionError)
        }
      }
    }

    return createdProduct
  }

  const onSubmit = async (values: ProductFormValues) => {
    try {
      setIsSubmitting(true)
      setValidationErrors({})
      setValidationWarnings({})

      const formData = transformFormDataForSubmission(values) as ProductFormData

      if (product) {
        await updateProduct.mutateAsync({
          productId: product.id,
          productData: formData,
        })
      } else {
        await createProductWithInitialStock(formData)
      }

      onSuccess?.()
      onOpenChange(false)
    } catch (error) {
      console.error('Failed to save product:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleSkuGenerate = () => {
    if (productName) {
      const suggestions = generateSkuSuggestions(productName, categoryId === '__none__' ? undefined : categoryId)
      if (suggestions.length > 0) {
        form.setValue('sku', suggestions[0])
      }
    }
  }

  const handlePricingChange = () => {
    const pricingValidation = validatePricing(costPrice, sellingPrice)
    
    if (pricingValidation.errors.length > 0) {
      setValidationErrors(prev => ({
        ...prev,
        selling_price: pricingValidation.errors
      }))
    } else {
      setValidationErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors.selling_price
        return newErrors
      })
    }
    
    if (pricingValidation.warnings.length > 0) {
      setValidationWarnings(prev => ({
        ...prev,
        selling_price: pricingValidation.warnings
      }))
    } else {
      setValidationWarnings(prev => {
        const newWarnings = { ...prev }
        delete newWarnings.selling_price
        return newWarnings
      })
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-scroll">
        <DialogHeader>
          <DialogTitle>
            {product ? 'Edit Product' : 'Add New Product'}
          </DialogTitle>
          <DialogDescription>
            {product 
              ? 'Update the product information below.' 
              : 'Fill in the details to create a new product.'
            }
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <ProductBasicInfo
              form={form}
              categories={categories}
              skuSuggestions={skuSuggestions}
              selectedImage={selectedImage}
              imagePreview={imagePreview}
              onImageSelect={setSelectedImage}
              onImagePreviewChange={setImagePreview}
              onSkuGenerate={handleSkuGenerate}
            />

            <Separator />

            <ProductPricing
              form={form}
              costPrice={costPrice}
              sellingPrice={sellingPrice}
              marginPercentage={marginPercentage}
              showPricingHelp={showPricingHelp}
              onPricingHelpToggle={() => setShowPricingHelp(!showPricingHelp)}
              onPricingChange={handlePricingChange}
            />

            <Separator />

            <ProductInventory
              form={form}
              trackInventory={trackInventory}
              product={product}
            />

            <Separator />

            <ProductPhysical form={form} />

            <Separator />

            <ProductSettings form={form} />

            <DialogFooter>
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => onOpenChange(false)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? 'Saving...' : (product ? 'Update Product' : 'Create Product')}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
