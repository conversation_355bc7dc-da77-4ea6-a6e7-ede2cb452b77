-- =====================================================
-- EMAIL BOUNCE HANDLING MIGRATION
-- =====================================================
-- Migration: 20250722_email_bounce_handling.sql
-- Description: Enhanced email bounce handling and retry mechanisms
-- Author: Kaya Finance Team
-- Date: 2025-07-22

-- =====================================================
-- STEP 1: ENHANCE EMAIL DELIVERIES TABLE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '📧 Enhancing email bounce handling...';
END $$;

-- Add bounce handling columns to email_deliveries
ALTER TABLE email_deliveries 
ADD COLUMN IF NOT EXISTS bounce_type TEXT CHECK (bounce_type IN ('hard', 'soft', 'complaint', 'suppression')),
ADD COLUMN IF NOT EXISTS bounce_reason TEXT,
ADD COLUMN IF NOT EXISTS bounce_details JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS retry_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS max_retries INTEGER DEFAULT 3,
ADD COLUMN IF NOT EXISTS next_retry_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS final_failure_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS webhook_data JSONB DEFAULT '{}';

-- =====================================================
-- STEP 2: CREATE EMAIL BOUNCE TRACKING TABLES
-- =====================================================

-- Email bounce events table for detailed tracking
CREATE TABLE IF NOT EXISTS email_bounce_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    delivery_id TEXT NOT NULL REFERENCES email_deliveries(id) ON DELETE CASCADE,
    event_type TEXT NOT NULL CHECK (event_type IN ('bounce', 'complaint', 'delivery', 'open', 'click', 'reject')),
    bounce_type TEXT CHECK (bounce_type IN ('hard', 'soft', 'complaint', 'suppression')),
    bounce_subtype TEXT,
    reason TEXT,
    diagnostic_code TEXT,
    feedback_id TEXT,
    user_agent TEXT,
    ip_address INET,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    raw_data JSONB DEFAULT '{}',
    processed BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Email suppression list for bounced/complained emails
CREATE TABLE IF NOT EXISTS email_suppressions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email TEXT NOT NULL UNIQUE,
    suppression_type TEXT NOT NULL CHECK (suppression_type IN ('bounce', 'complaint', 'manual', 'unsubscribe')),
    reason TEXT,
    bounce_count INTEGER DEFAULT 0,
    last_bounce_at TIMESTAMP WITH TIME ZONE,
    suppressed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    is_permanent BOOLEAN DEFAULT false,
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Email retry queue for failed deliveries
CREATE TABLE IF NOT EXISTS email_retry_queue (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    delivery_id TEXT NOT NULL REFERENCES email_deliveries(id) ON DELETE CASCADE,
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    email_type TEXT NOT NULL,
    recipient TEXT NOT NULL,
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    next_retry_at TIMESTAMP WITH TIME ZONE NOT NULL,
    last_error TEXT,
    original_data JSONB NOT NULL,
    priority INTEGER DEFAULT 1,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- STEP 3: CREATE INDEXES FOR PERFORMANCE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '📊 Creating bounce handling indexes...';
END $$;

-- Email bounce events indexes
CREATE INDEX IF NOT EXISTS idx_email_bounce_events_delivery_id ON email_bounce_events(delivery_id);
CREATE INDEX IF NOT EXISTS idx_email_bounce_events_event_type ON email_bounce_events(event_type);
CREATE INDEX IF NOT EXISTS idx_email_bounce_events_timestamp ON email_bounce_events(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_email_bounce_events_processed ON email_bounce_events(processed) WHERE processed = false;

-- Email suppressions indexes
CREATE INDEX IF NOT EXISTS idx_email_suppressions_email ON email_suppressions(email);
CREATE INDEX IF NOT EXISTS idx_email_suppressions_type ON email_suppressions(suppression_type);
CREATE INDEX IF NOT EXISTS idx_email_suppressions_expires_at ON email_suppressions(expires_at) WHERE expires_at IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_email_suppressions_permanent ON email_suppressions(is_permanent) WHERE is_permanent = true;

-- Email retry queue indexes
CREATE INDEX IF NOT EXISTS idx_email_retry_queue_next_retry_at ON email_retry_queue(next_retry_at) WHERE status = 'pending';
CREATE INDEX IF NOT EXISTS idx_email_retry_queue_status ON email_retry_queue(status);
CREATE INDEX IF NOT EXISTS idx_email_retry_queue_org_id ON email_retry_queue(org_id);
CREATE INDEX IF NOT EXISTS idx_email_retry_queue_priority ON email_retry_queue(priority DESC, next_retry_at ASC);

-- =====================================================
-- STEP 4: CREATE BOUNCE HANDLING FUNCTIONS
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '⚙️ Creating bounce handling functions...';
END $$;

-- Function to check if email is suppressed
CREATE OR REPLACE FUNCTION is_email_suppressed(email_address TEXT)
RETURNS BOOLEAN AS $$
DECLARE
    suppression_record email_suppressions%ROWTYPE;
BEGIN
    SELECT * INTO suppression_record
    FROM email_suppressions
    WHERE email = LOWER(email_address)
    AND (expires_at IS NULL OR expires_at > NOW())
    AND (is_permanent = true OR suppression_type IN ('bounce', 'complaint'));
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to add email to suppression list
CREATE OR REPLACE FUNCTION suppress_email(
    email_address TEXT,
    suppression_type_param TEXT,
    reason_param TEXT DEFAULT NULL,
    is_permanent_param BOOLEAN DEFAULT false,
    expires_hours INTEGER DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    suppression_id UUID;
    expires_at_param TIMESTAMP WITH TIME ZONE;
BEGIN
    -- Calculate expiration if specified
    IF expires_hours IS NOT NULL AND NOT is_permanent_param THEN
        expires_at_param := NOW() + INTERVAL '1 hour' * expires_hours;
    END IF;
    
    -- Insert or update suppression record
    INSERT INTO email_suppressions (
        email, 
        suppression_type, 
        reason, 
        is_permanent, 
        expires_at,
        bounce_count,
        last_bounce_at
    )
    VALUES (
        LOWER(email_address),
        suppression_type_param,
        reason_param,
        is_permanent_param,
        expires_at_param,
        CASE WHEN suppression_type_param = 'bounce' THEN 1 ELSE 0 END,
        CASE WHEN suppression_type_param = 'bounce' THEN NOW() ELSE NULL END
    )
    ON CONFLICT (email) DO UPDATE SET
        suppression_type = EXCLUDED.suppression_type,
        reason = EXCLUDED.reason,
        bounce_count = email_suppressions.bounce_count + CASE WHEN EXCLUDED.suppression_type = 'bounce' THEN 1 ELSE 0 END,
        last_bounce_at = CASE WHEN EXCLUDED.suppression_type = 'bounce' THEN NOW() ELSE email_suppressions.last_bounce_at END,
        is_permanent = EXCLUDED.is_permanent OR email_suppressions.is_permanent,
        expires_at = CASE 
            WHEN EXCLUDED.is_permanent OR email_suppressions.is_permanent THEN NULL
            ELSE EXCLUDED.expires_at
        END,
        updated_at = NOW()
    RETURNING id INTO suppression_id;
    
    RETURN suppression_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to process bounce event
CREATE OR REPLACE FUNCTION process_bounce_event(
    delivery_id_param TEXT,
    event_type_param TEXT,
    bounce_type_param TEXT DEFAULT NULL,
    reason_param TEXT DEFAULT NULL,
    raw_data_param JSONB DEFAULT '{}'
)
RETURNS UUID AS $$
DECLARE
    event_id UUID;
    delivery_record email_deliveries%ROWTYPE;
    should_suppress BOOLEAN := false;
    suppression_hours INTEGER;
BEGIN
    -- Get delivery record
    SELECT * INTO delivery_record FROM email_deliveries WHERE id = delivery_id_param;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Delivery record not found: %', delivery_id_param;
    END IF;
    
    -- Insert bounce event
    INSERT INTO email_bounce_events (
        delivery_id,
        event_type,
        bounce_type,
        reason,
        timestamp,
        raw_data
    )
    VALUES (
        delivery_id_param,
        event_type_param,
        bounce_type_param,
        reason_param,
        NOW(),
        raw_data_param
    )
    RETURNING id INTO event_id;
    
    -- Update delivery record
    UPDATE email_deliveries SET
        status = CASE 
            WHEN event_type_param = 'bounce' THEN 'bounced'
            WHEN event_type_param = 'complaint' THEN 'bounced'
            ELSE status
        END,
        bounced_at = CASE 
            WHEN event_type_param IN ('bounce', 'complaint') THEN NOW()
            ELSE bounced_at
        END,
        bounce_type = bounce_type_param,
        bounce_reason = reason_param,
        updated_at = NOW()
    WHERE id = delivery_id_param;
    
    -- Determine if email should be suppressed
    IF event_type_param = 'bounce' THEN
        IF bounce_type_param = 'hard' THEN
            should_suppress := true;
            suppression_hours := NULL; -- Permanent
        ELSIF bounce_type_param = 'soft' THEN
            -- Check bounce count for this email
            IF (SELECT COUNT(*) FROM email_bounce_events ebe 
                JOIN email_deliveries ed ON ebe.delivery_id = ed.id 
                WHERE ed.email = delivery_record.email 
                AND ebe.event_type = 'bounce' 
                AND ebe.timestamp > NOW() - INTERVAL '24 hours') >= 3 THEN
                should_suppress := true;
                suppression_hours := 24; -- Temporary suppression
            END IF;
        END IF;
    ELSIF event_type_param = 'complaint' THEN
        should_suppress := true;
        suppression_hours := NULL; -- Permanent
    END IF;
    
    -- Suppress email if needed
    IF should_suppress THEN
        PERFORM suppress_email(
            delivery_record.email,
            event_type_param,
            reason_param,
            suppression_hours IS NULL,
            suppression_hours
        );
    END IF;
    
    RETURN event_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to add email to retry queue
CREATE OR REPLACE FUNCTION add_to_retry_queue(
    delivery_id_param TEXT,
    org_id_param UUID,
    email_type_param TEXT,
    recipient_param TEXT,
    original_data_param JSONB,
    priority_param INTEGER DEFAULT 1
)
RETURNS UUID AS $$
DECLARE
    retry_id UUID;
    retry_delay_minutes INTEGER;
    current_retry_count INTEGER;
BEGIN
    -- Get current retry count for this delivery
    SELECT COALESCE(retry_count, 0) INTO current_retry_count
    FROM email_deliveries
    WHERE id = delivery_id_param;

    -- Calculate retry delay with exponential backoff
    retry_delay_minutes := 5 * POWER(2, current_retry_count); -- 5, 10, 20, 40 minutes

    -- Insert into retry queue
    INSERT INTO email_retry_queue (
        delivery_id,
        org_id,
        email_type,
        recipient,
        retry_count,
        next_retry_at,
        original_data,
        priority
    )
    VALUES (
        delivery_id_param,
        org_id_param,
        email_type_param,
        recipient_param,
        current_retry_count,
        NOW() + INTERVAL '1 minute' * retry_delay_minutes,
        original_data_param,
        priority_param
    )
    RETURNING id INTO retry_id;

    RETURN retry_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get next emails to retry
CREATE OR REPLACE FUNCTION get_emails_to_retry(batch_size INTEGER DEFAULT 10)
RETURNS TABLE (
    retry_id UUID,
    delivery_id TEXT,
    org_id UUID,
    email_type TEXT,
    recipient TEXT,
    retry_count INTEGER,
    original_data JSONB
) AS $$
BEGIN
    RETURN QUERY
    UPDATE email_retry_queue
    SET status = 'processing', updated_at = NOW()
    WHERE id IN (
        SELECT erq.id
        FROM email_retry_queue erq
        WHERE erq.status = 'pending'
        AND erq.next_retry_at <= NOW()
        AND erq.retry_count < erq.max_retries
        AND NOT is_email_suppressed(erq.recipient)
        ORDER BY erq.priority DESC, erq.next_retry_at ASC
        LIMIT batch_size
        FOR UPDATE SKIP LOCKED
    )
    RETURNING
        email_retry_queue.id,
        email_retry_queue.delivery_id,
        email_retry_queue.org_id,
        email_retry_queue.email_type,
        email_retry_queue.recipient,
        email_retry_queue.retry_count,
        email_retry_queue.original_data;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to mark retry as completed
CREATE OR REPLACE FUNCTION complete_retry(
    retry_id_param UUID,
    success BOOLEAN,
    error_message TEXT DEFAULT NULL
)
RETURNS VOID AS $$
DECLARE
    retry_record email_retry_queue%ROWTYPE;
BEGIN
    SELECT * INTO retry_record FROM email_retry_queue WHERE id = retry_id_param;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'Retry record not found: %', retry_id_param;
    END IF;

    IF success THEN
        -- Mark as completed
        UPDATE email_retry_queue
        SET status = 'completed', updated_at = NOW()
        WHERE id = retry_id_param;

        -- Update original delivery record
        UPDATE email_deliveries
        SET status = 'sent', retry_count = retry_record.retry_count + 1
        WHERE id = retry_record.delivery_id;
    ELSE
        -- Increment retry count and schedule next retry or mark as failed
        IF retry_record.retry_count + 1 >= retry_record.max_retries THEN
            -- Max retries reached, mark as failed
            UPDATE email_retry_queue
            SET status = 'failed', last_error = error_message, updated_at = NOW()
            WHERE id = retry_id_param;

            UPDATE email_deliveries
            SET status = 'failed',
                retry_count = retry_record.retry_count + 1,
                final_failure_at = NOW(),
                error_message = error_message
            WHERE id = retry_record.delivery_id;
        ELSE
            -- Schedule next retry
            UPDATE email_retry_queue
            SET retry_count = retry_record.retry_count + 1,
                next_retry_at = NOW() + INTERVAL '1 minute' * (5 * POWER(2, retry_record.retry_count + 1)),
                last_error = error_message,
                status = 'pending',
                updated_at = NOW()
            WHERE id = retry_id_param;

            UPDATE email_deliveries
            SET retry_count = retry_record.retry_count + 1,
                next_retry_at = NOW() + INTERVAL '1 minute' * (5 * POWER(2, retry_record.retry_count + 1))
            WHERE id = retry_record.delivery_id;
        END IF;
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to clean up old retry queue entries
CREATE OR REPLACE FUNCTION cleanup_retry_queue(days_old INTEGER DEFAULT 7)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM email_retry_queue
    WHERE status IN ('completed', 'failed', 'cancelled')
    AND updated_at < NOW() - INTERVAL '1 day' * days_old;

    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- STEP 5: CREATE TRIGGERS
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔧 Creating bounce handling triggers...';
END $$;

-- Add updated_at triggers
DROP TRIGGER IF EXISTS update_email_suppressions_updated_at ON email_suppressions;
CREATE TRIGGER update_email_suppressions_updated_at
    BEFORE UPDATE ON email_suppressions
    FOR EACH ROW EXECUTE FUNCTION update_email_updated_at_column();

DROP TRIGGER IF EXISTS update_email_retry_queue_updated_at ON email_retry_queue;
CREATE TRIGGER update_email_retry_queue_updated_at
    BEFORE UPDATE ON email_retry_queue
    FOR EACH ROW EXECUTE FUNCTION update_email_updated_at_column();

-- =====================================================
-- STEP 6: CREATE RLS POLICIES
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔒 Creating bounce handling RLS policies...';
END $$;

-- Enable RLS on new tables
ALTER TABLE email_bounce_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_suppressions ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_retry_queue ENABLE ROW LEVEL SECURITY;

-- Email bounce events policies
CREATE POLICY "email_bounce_events_select_policy" ON email_bounce_events
    FOR SELECT USING (
        auth.role() = 'service_role' OR
        delivery_id IN (
            SELECT ed.id FROM email_deliveries ed
            JOIN profiles p ON ed.org_id = p.org_id
            WHERE p.id = auth.uid()
        )
    );

CREATE POLICY "email_bounce_events_insert_policy" ON email_bounce_events
    FOR INSERT WITH CHECK (auth.role() = 'service_role');

-- Email suppressions policies (admin only)
CREATE POLICY "email_suppressions_select_policy" ON email_suppressions
    FOR SELECT USING (
        auth.role() = 'service_role' OR
        EXISTS (
            SELECT 1 FROM profiles p
            WHERE p.id = auth.uid()
            AND p.role = 'admin'
        )
    );

CREATE POLICY "email_suppressions_insert_policy" ON email_suppressions
    FOR INSERT WITH CHECK (
        auth.role() = 'service_role' OR
        EXISTS (
            SELECT 1 FROM profiles p
            WHERE p.id = auth.uid()
            AND p.role = 'admin'
        )
    );

CREATE POLICY "email_suppressions_update_policy" ON email_suppressions
    FOR UPDATE USING (
        auth.role() = 'service_role' OR
        EXISTS (
            SELECT 1 FROM profiles p
            WHERE p.id = auth.uid()
            AND p.role = 'admin'
        )
    );

-- Email retry queue policies
CREATE POLICY "email_retry_queue_select_policy" ON email_retry_queue
    FOR SELECT USING (
        auth.role() = 'service_role' OR
        org_id IN (SELECT p.org_id FROM profiles p WHERE p.id = auth.uid())
    );

CREATE POLICY "email_retry_queue_insert_policy" ON email_retry_queue
    FOR INSERT WITH CHECK (auth.role() = 'service_role');

CREATE POLICY "email_retry_queue_update_policy" ON email_retry_queue
    FOR UPDATE USING (auth.role() = 'service_role');

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '✅ EMAIL BOUNCE HANDLING MIGRATION COMPLETED!';
    RAISE NOTICE '====================================================';
    RAISE NOTICE '';
    RAISE NOTICE '📧 ENHANCED TABLES:';
    RAISE NOTICE '  • email_deliveries - Added bounce tracking columns';
    RAISE NOTICE '  • email_bounce_events - Detailed bounce event tracking';
    RAISE NOTICE '  • email_suppressions - Bounce suppression management';
    RAISE NOTICE '  • email_retry_queue - Failed email retry system';
    RAISE NOTICE '';
    RAISE NOTICE '⚙️ CREATED FUNCTIONS:';
    RAISE NOTICE '  • is_email_suppressed() - Check suppression status';
    RAISE NOTICE '  • suppress_email() - Add email to suppression list';
    RAISE NOTICE '  • process_bounce_event() - Process bounce events';
    RAISE NOTICE '  • add_to_retry_queue() - Queue failed emails for retry';
    RAISE NOTICE '  • get_emails_to_retry() - Get next batch to retry';
    RAISE NOTICE '  • complete_retry() - Mark retry completion';
    RAISE NOTICE '  • cleanup_retry_queue() - Clean old retry records';
    RAISE NOTICE '';
    RAISE NOTICE '🔒 CREATED RLS POLICIES:';
    RAISE NOTICE '  • Organization-based access control';
    RAISE NOTICE '  • Admin-only suppression management';
    RAISE NOTICE '';
    RAISE NOTICE '🎉 Email bounce handling is ready!';
    RAISE NOTICE '';
END $$;
