
import { useState, useEffect, useCallback } from 'react'
import { useAuth } from '@/hooks/useAuthHook'
import { supabase } from '@/lib/supabase'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Plus, Trash } from 'lucide-react'
import { toast } from 'sonner'
import { formatCurrency } from '@/lib/utils'
import type { Payment, Invoice, Bill } from '@/types/database'

interface PaymentApplication {
  id?: string
  applied_to_type: string
  applied_to_id: string
  amount_applied: number
  document_number?: string
  document_date?: string
  amount_due?: number
}

interface PaymentApplicationDialogProps {
  payment: Payment
  onClose: () => void
  onSave: () => void
}

export function PaymentApplicationDialog({ payment, onClose, onSave }: PaymentApplicationDialogProps) {
  const { profile } = useAuth()
  const [applications, setApplications] = useState<PaymentApplication[]>([])
  const [availableDocuments, setAvailableDocuments] = useState<(Invoice | Bill)[]>([])
  const [newApplication, setNewApplication] = useState({
    applied_to_type: payment.payee_type === 'customer' ? 'invoice' : 'bill',
    applied_to_id: '',
    amount_applied: 0
  })
  const [isLoading, setIsLoading] = useState(false)

  const fetchApplications = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('payment_applications')
        .select('*')
        .eq('payment_id', payment.id)

      if (error) throw error

      // Enhance applications with document details
      const enhancedApplications = await Promise.all(
        (data || []).map(async (app) => {
          let documentDetails = {}
          
          if (app.applied_to_type === 'invoice') {
            const { data: invoice } = await supabase
              .from('invoices')
              .select('invoice_number, date_issued, amount_due')
              .eq('id', app.applied_to_id)
              .single()
            
            if (invoice) {
              documentDetails = {
                document_number: invoice.invoice_number,
                document_date: invoice.date_issued,
                amount_due: invoice.amount_due
              }
            }
          } else if (app.applied_to_type === 'bill') {
            const { data: bill } = await supabase
              .from('bills')
              .select('bill_number, date_issued, amount_due')
              .eq('id', app.applied_to_id)
              .single()
            
            if (bill) {
              documentDetails = {
                document_number: bill.bill_number,
                document_date: bill.date_issued,
                amount_due: bill.amount_due
              }
            }
          }

          return { ...app, ...documentDetails }
        })
      )

      setApplications(enhancedApplications)
    } catch (error) {
      console.error('Error fetching applications:', error)
    }
  }, [payment.id])

  const fetchAvailableDocuments = useCallback(async () => {
    if (!profile?.org_id) return

    try {
      if (payment.payee_type === 'customer') {
        // Fetch invoices for this customer
        const { data, error } = await supabase
          .from('invoices')
          .select('*')
          .eq('org_id', profile.org_id)
          .eq('customer_id', payment.payee_id)
          .gt('amount_due', 0)

        if (error) throw error
        setAvailableDocuments(data || [])
      } else {
        // Fetch bills for this vendor
        const { data, error } = await supabase
          .from('bills')
          .select('*')
          .eq('org_id', profile.org_id)
          .eq('vendor_id', payment.payee_id)
          .gt('amount_due', 0)

        if (error) throw error
        setAvailableDocuments(data || [])
      }
    } catch (error) {
      console.error('Error fetching documents:', error)
    }
  }, [profile?.org_id, payment.payee_type, payment.payee_id])

  useEffect(() => {
    fetchApplications()
    fetchAvailableDocuments()
  }, [payment.id, fetchApplications, fetchAvailableDocuments])

  const handleAddApplication = async () => {
    if (!newApplication.applied_to_id || newApplication.amount_applied <= 0) {
      toast.error('Please select a document and enter a valid amount')
      return
    }

    setIsLoading(true)
    try {
      const { error } = await supabase
        .from('payment_applications')
        .insert([{
          payment_id: payment.id,
          applied_to_type: newApplication.applied_to_type,
          applied_to_id: newApplication.applied_to_id,
          amount_applied: newApplication.amount_applied
        }])

      if (error) throw error

      toast.success('Application added successfully')
      setNewApplication({
        applied_to_type: payment.payee_type === 'customer' ? 'invoice' : 'bill',
        applied_to_id: '',
        amount_applied: 0
      })
      fetchApplications()
      fetchAvailableDocuments()
    } catch (error) {
      console.error('Error adding application:', error)
      toast.error('Failed to add application')
    } finally {
      setIsLoading(false)
    }
  }

  const handleRemoveApplication = async (applicationId: string) => {
    if (!confirm('Are you sure you want to remove this application?')) return

    try {
      const { error } = await supabase
        .from('payment_applications')
        .delete()
        .eq('id', applicationId)

      if (error) throw error

      toast.success('Application removed successfully')
      fetchApplications()
      fetchAvailableDocuments()
    } catch (error) {
      console.error('Error removing application:', error)
      toast.error('Failed to remove application')
    }
  }

  const totalApplied = applications.reduce((sum, app) => sum + Number(app.amount_applied), 0)
  const remainingBalance = Number(payment.amount) - totalApplied

  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-scroll">
        <DialogHeader>
          <DialogTitle>Payment Applications</DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Payment Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Payment Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-4 gap-4">
                <div>
                  <Label>Payment Amount</Label>
                  <p className="font-semibold">{formatCurrency(Number(payment.amount))}</p>
                </div>
                <div>
                  <Label>Total Applied</Label>
                  <p className="font-semibold">{formatCurrency(totalApplied)}</p>
                </div>
                <div>
                  <Label>Remaining Balance</Label>
                  <p className="font-semibold text-blue-600">{formatCurrency(remainingBalance)}</p>
                </div>
                <div>
                  <Label>Status</Label>
                  <Badge variant={remainingBalance > 0 ? 'secondary' : 'default'}>
                    {remainingBalance > 0 ? 'Partially Applied' : 'Fully Applied'}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Current Applications */}
          <Card>
            <CardHeader>
              <CardTitle>Current Applications</CardTitle>
            </CardHeader>
            <CardContent>
              {applications.length === 0 ? (
                <p className="text-gray-500">No applications yet</p>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-2">Document</th>
                        <th className="text-left p-2">Date</th>
                        <th className="text-left p-2">Amount Due</th>
                        <th className="text-left p-2">Applied</th>
                        <th className="text-left p-2">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {applications.map((app) => (
                        <tr key={app.id} className="border-b">
                          <td className="p-2">{app.document_number}</td>
                          <td className="p-2">{app.document_date ? new Date(app.document_date).toLocaleDateString() : '-'}</td>
                          <td className="p-2">{app.amount_due ? formatCurrency(Number(app.amount_due)) : '-'}</td>
                          <td className="p-2">{formatCurrency(Number(app.amount_applied))}</td>
                          <td className="p-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => app.id && handleRemoveApplication(app.id)}
                            >
                              <Trash className="w-3 h-3" />
                            </Button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Add New Application */}
          {remainingBalance > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Add New Application</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="document">Document</Label>
                    <Select
                      value={newApplication.applied_to_id}
                      onValueChange={(value) => {
                        const document = availableDocuments.find(d => d.id === value)
                        setNewApplication({
                          ...newApplication,
                          applied_to_id: value,
                          amount_applied: document ? Math.min(Number(document.amount_due), remainingBalance) : 0
                        })
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select document" />
                      </SelectTrigger>
                      <SelectContent>
                        {availableDocuments.map((doc) => (
                          <SelectItem key={doc.id} value={doc.id}>
                            {'invoice_number' in doc ? doc.invoice_number : doc.bill_number} - {formatCurrency(Number(doc.amount_due))}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="amount">Amount to Apply</Label>
                    <Input
                      type="number"
                      step="0.01"
                      value={newApplication.amount_applied}
                      onChange={(e) => setNewApplication({
                        ...newApplication,
                        amount_applied: Number(e.target.value)
                      })}
                      max={remainingBalance}
                    />
                  </div>

                  <div className="flex items-end">
                    <Button onClick={handleAddApplication} disabled={isLoading}>
                      <Plus className="w-4 h-4 mr-2" />
                      Add Application
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          <div className="flex justify-end space-x-2 pt-4">
            <Button onClick={onSave}>
              Done
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
