/**
 * Email Configuration Service
 * Manages email service provider configuration and credentials
 */

import { config } from '@/lib/config'

export type EmailProvider = 'resend' | 'sendgrid' | 'aws-ses'

export interface EmailCredentials {
  provider: EmailProvider
  apiKey?: string
  region?: string
  accessKeyId?: string
  secretAccessKey?: string
}

export interface EmailSenderConfig {
  fromEmail: string
  fromName: string
  replyToEmail: string
  supportEmail: string
}

export interface EmailBrandingConfig {
  companyName: string
  companyTagline: string
  logoUrl: string
  websiteUrl: string
  primaryColor: string
}

export interface EmailRateLimitConfig {
  perMinute: number
  perHour: number
  perDay: number
  burstLimit: number
}

export interface EmailBounceConfig {
  webhookSecret: string
  maxBounceCount: number
  cooldownHours: number
}

export interface EmailRetryConfig {
  maxRetries: number
  delayMinutes: number
  backoffMultiplier: number
}

export interface EmailMonitoringConfig {
  enabled: boolean
  bounceRateThreshold: number
  failureRateThreshold: number
  alertEmail: string
}

export interface EmailConfiguration {
  credentials: EmailCredentials
  sender: EmailSenderConfig
  branding: EmailBrandingConfig
  rateLimits: EmailRateLimitConfig
  bounceHandling: EmailBounceConfig
  retryConfig: EmailRetryConfig
  monitoring: EmailMonitoringConfig
}

/**
 * Get email configuration from environment variables
 */
export function getEmailConfiguration(): EmailConfiguration {
  const provider = (process.env.VITE_EMAIL_PROVIDER || 'resend') as EmailProvider

  // Validate required environment variables
  validateEmailEnvironment(provider)

  return {
    credentials: getEmailCredentials(provider),
    sender: getEmailSenderConfig(),
    branding: getEmailBrandingConfig(),
    rateLimits: getEmailRateLimitConfig(),
    bounceHandling: getEmailBounceConfig(),
    retryConfig: getEmailRetryConfig(),
    monitoring: getEmailMonitoringConfig()
  }
}

/**
 * Get email credentials based on provider
 */
function getEmailCredentials(provider: EmailProvider): EmailCredentials {
  switch (provider) {
    case 'resend':
      return {
        provider: 'resend',
        apiKey: process.env.RESEND_API_KEY
      }
    
    case 'sendgrid':
      return {
        provider: 'sendgrid',
        apiKey: process.env.SENDGRID_API_KEY
      }
    
    case 'aws-ses':
      return {
        provider: 'aws-ses',
        region: process.env.AWS_SES_REGION || 'us-east-1',
        accessKeyId: process.env.AWS_SES_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SES_SECRET_ACCESS_KEY
      }
    
    default:
      throw new Error(`Unsupported email provider: ${provider}`)
  }
}

/**
 * Get email sender configuration
 */
function getEmailSenderConfig(): EmailSenderConfig {
  return {
    fromEmail: process.env.VITE_FROM_EMAIL || '<EMAIL>',
    fromName: process.env.VITE_FROM_NAME || 'KAYA Finance',
    replyToEmail: process.env.VITE_REPLY_TO_EMAIL || process.env.VITE_FROM_EMAIL || '<EMAIL>',
    supportEmail: process.env.VITE_SUPPORT_EMAIL || '<EMAIL>'
  }
}

/**
 * Get email branding configuration
 */
function getEmailBrandingConfig(): EmailBrandingConfig {
  return {
    companyName: process.env.VITE_COMPANY_NAME || 'KAYA Finance',
    companyTagline: process.env.VITE_COMPANY_TAGLINE || 'Modern Financial Management for SMEs',
    logoUrl: process.env.VITE_COMPANY_LOGO_URL || 'https://kayafinance.com/logo.png',
    websiteUrl: process.env.VITE_COMPANY_WEBSITE || 'https://kayafinance.com',
    primaryColor: process.env.VITE_COMPANY_PRIMARY_COLOR || '#2563eb'
  }
}

/**
 * Get email rate limit configuration
 */
function getEmailRateLimitConfig(): EmailRateLimitConfig {
  return {
    perMinute: parseInt(process.env.EMAIL_RATE_LIMIT_PER_MINUTE || '10'),
    perHour: parseInt(process.env.EMAIL_RATE_LIMIT_PER_HOUR || '100'),
    perDay: parseInt(process.env.EMAIL_RATE_LIMIT_PER_DAY || '1000'),
    burstLimit: parseInt(process.env.EMAIL_BURST_LIMIT || '5')
  }
}

/**
 * Get email bounce handling configuration
 */
function getEmailBounceConfig(): EmailBounceConfig {
  return {
    webhookSecret: process.env.EMAIL_BOUNCE_WEBHOOK_SECRET || '',
    maxBounceCount: parseInt(process.env.EMAIL_MAX_BOUNCE_COUNT || '3'),
    cooldownHours: parseInt(process.env.EMAIL_BOUNCE_COOLDOWN_HOURS || '24')
  }
}

/**
 * Get email retry configuration
 */
function getEmailRetryConfig(): EmailRetryConfig {
  return {
    maxRetries: parseInt(process.env.EMAIL_MAX_RETRIES || '3'),
    delayMinutes: parseInt(process.env.EMAIL_RETRY_DELAY_MINUTES || '5'),
    backoffMultiplier: parseFloat(process.env.EMAIL_RETRY_BACKOFF_MULTIPLIER || '2')
  }
}

/**
 * Get email monitoring configuration
 */
function getEmailMonitoringConfig(): EmailMonitoringConfig {
  return {
    enabled: process.env.EMAIL_MONITORING_ENABLED === 'true',
    bounceRateThreshold: parseFloat(process.env.EMAIL_ALERT_THRESHOLD_BOUNCE_RATE || '5'),
    failureRateThreshold: parseFloat(process.env.EMAIL_ALERT_THRESHOLD_FAILURE_RATE || '10'),
    alertEmail: process.env.EMAIL_ALERT_EMAIL || '<EMAIL>'
  }
}

/**
 * Validate email environment configuration
 */
function validateEmailEnvironment(provider: EmailProvider): void {
  const requiredVars: string[] = []

  switch (provider) {
    case 'resend':
      if (!process.env.RESEND_API_KEY) {
        requiredVars.push('RESEND_API_KEY')
      }
      break
    
    case 'sendgrid':
      if (!process.env.SENDGRID_API_KEY) {
        requiredVars.push('SENDGRID_API_KEY')
      }
      break
    
    case 'aws-ses':
      if (!process.env.AWS_SES_ACCESS_KEY_ID) {
        requiredVars.push('AWS_SES_ACCESS_KEY_ID')
      }
      if (!process.env.AWS_SES_SECRET_ACCESS_KEY) {
        requiredVars.push('AWS_SES_SECRET_ACCESS_KEY')
      }
      break
  }

  if (requiredVars.length > 0) {
    throw new Error(`Missing required environment variables for ${provider}: ${requiredVars.join(', ')}`)
  }
}

/**
 * Test email configuration
 */
export async function testEmailConfiguration(): Promise<{ success: boolean; message: string }> {
  try {
    const emailConfig = getEmailConfiguration()
    
    // Basic validation
    if (!emailConfig.credentials.apiKey && emailConfig.credentials.provider !== 'aws-ses') {
      return { success: false, message: 'Email API key is not configured' }
    }
    
    if (emailConfig.credentials.provider === 'aws-ses') {
      if (!emailConfig.credentials.accessKeyId || !emailConfig.credentials.secretAccessKey) {
        return { success: false, message: 'AWS SES credentials are not configured' }
      }
    }
    
    return { success: true, message: 'Email configuration is valid' }
  } catch (error) {
    return { 
      success: false, 
      message: error instanceof Error ? error.message : 'Unknown configuration error' 
    }
  }
}

/**
 * Get email service status
 */
export function getEmailServiceStatus(): {
  provider: EmailProvider
  configured: boolean
  rateLimitsEnabled: boolean
  bounceHandlingEnabled: boolean
  monitoringEnabled: boolean
} {
  try {
    const emailConfig = getEmailConfiguration()
    
    return {
      provider: emailConfig.credentials.provider,
      configured: true,
      rateLimitsEnabled: emailConfig.rateLimits.perHour > 0,
      bounceHandlingEnabled: !!emailConfig.bounceHandling.webhookSecret,
      monitoringEnabled: emailConfig.monitoring.enabled
    }
  } catch (error) {
    return {
      provider: 'resend',
      configured: false,
      rateLimitsEnabled: false,
      bounceHandlingEnabled: false,
      monitoringEnabled: false
    }
  }
}
