/**
 * Kaya Finance - Security Configuration
 * This file contains security-related configurations for production deployment
 */

// Content Security Policy configuration
export const cspConfig = {
  directives: {
    defaultSrc: ["'self'"],
    scriptSrc: [
      "'self'",
      "'unsafe-inline'", // Required for some React functionality
      "'unsafe-eval'", // Required for development, remove in production if possible
      "https://cdn.jsdelivr.net",
      "https://unpkg.com",
    ],
    styleSrc: [
      "'self'",
      "'unsafe-inline'", // Required for CSS-in-JS and Tailwind
      "https://fonts.googleapis.com",
    ],
    fontSrc: [
      "'self'",
      "data:",
      "https://fonts.gstatic.com",
    ],
    imgSrc: [
      "'self'",
      "data:",
      "https:",
      "blob:",
    ],
    connectSrc: [
      "'self'",
      "https://*.supabase.co",
      "wss://*.supabase.co",
      "https://api.resend.com",
      "https://sentry.io",
    ],
    frameSrc: ["'none'"],
    frameAncestors: ["'none'"],
    baseUri: ["'self'"],
    formAction: ["'self'"],
    objectSrc: ["'none'"],
    mediaSrc: ["'self'"],
    workerSrc: ["'self'", "blob:"],
    childSrc: ["'self'"],
    manifestSrc: ["'self'"],
  },
  reportUri: "/api/csp-report",
  reportOnly: false, // Set to true for testing, false for enforcement
};

// Security headers configuration
export const securityHeaders = {
  // Prevent clickjacking
  "X-Frame-Options": "DENY",
  
  // Prevent MIME type sniffing
  "X-Content-Type-Options": "nosniff",
  
  // XSS protection
  "X-XSS-Protection": "1; mode=block",
  
  // Referrer policy
  "Referrer-Policy": "strict-origin-when-cross-origin",
  
  // Permissions policy
  "Permissions-Policy": "camera=(), microphone=(), geolocation=(), payment=()",
  
  // HSTS (HTTP Strict Transport Security)
  "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload",
  
  // Expect-CT header
  "Expect-CT": "max-age=86400, enforce",
  
  // Cross-Origin policies
  "Cross-Origin-Embedder-Policy": "require-corp",
  "Cross-Origin-Opener-Policy": "same-origin",
  "Cross-Origin-Resource-Policy": "same-origin",
};

// Rate limiting configuration
export const rateLimitConfig = {
  // General API rate limiting
  api: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // Limit each IP to 100 requests per windowMs
    message: "Too many requests from this IP, please try again later.",
    standardHeaders: true,
    legacyHeaders: false,
  },
  
  // Authentication rate limiting
  auth: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // Limit each IP to 5 login attempts per windowMs
    message: "Too many login attempts, please try again later.",
    skipSuccessfulRequests: true,
  },
  
  // Password reset rate limiting
  passwordReset: {
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 3, // Limit each IP to 3 password reset requests per hour
    message: "Too many password reset attempts, please try again later.",
  },
  
  // File upload rate limiting
  upload: {
    windowMs: 60 * 1000, // 1 minute
    max: 10, // Limit each IP to 10 uploads per minute
    message: "Too many file uploads, please try again later.",
  },
};

// CORS configuration
export const corsConfig = {
  origin: [
    "https://app.kayafinance.com",
    "https://www.kayafinance.com",
    "https://kayafinance.com",
  ],
  methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
  allowedHeaders: [
    "Content-Type",
    "Authorization",
    "X-Requested-With",
    "Accept",
    "Origin",
  ],
  credentials: true,
  maxAge: 86400, // 24 hours
};

// Input validation rules
export const validationRules = {
  // Email validation
  email: {
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    maxLength: 254,
  },
  
  // Password validation
  password: {
    minLength: 8,
    maxLength: 128,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: false,
  },
  
  // File upload validation
  fileUpload: {
    maxSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: [
      "image/jpeg",
      "image/png",
      "image/gif",
      "image/webp",
      "application/pdf",
      "text/csv",
      "application/vnd.ms-excel",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    ],
    maxFiles: 5,
  },
  
  // Text input validation
  textInput: {
    maxLength: 1000,
    allowedChars: /^[a-zA-Z0-9\s\-_.,!?@#$%&*()+=[\]{}|;:'"<>\/\\]*$/,
  },
  
  // Numeric input validation
  numericInput: {
    min: -999999999.99,
    max: 999999999.99,
    decimalPlaces: 2,
  },
};

// Session configuration
export const sessionConfig = {
  // Session timeout (in milliseconds)
  timeout: 24 * 60 * 60 * 1000, // 24 hours
  
  // Refresh token timeout
  refreshTimeout: 7 * 24 * 60 * 60 * 1000, // 7 days
  
  // Session cookie configuration
  cookie: {
    httpOnly: true,
    secure: true, // HTTPS only
    sameSite: "strict",
    maxAge: 24 * 60 * 60 * 1000, // 24 hours
  },
  
  // CSRF protection
  csrf: {
    enabled: true,
    cookieName: "_csrf",
    headerName: "X-CSRF-Token",
  },
};

// Logging configuration
export const loggingConfig = {
  // Log levels: error, warn, info, debug
  level: "error", // Production should use 'error' or 'warn'
  
  // Log sensitive data (should be false in production)
  logSensitiveData: false,
  
  // Log retention (in days)
  retention: 30,
  
  // Security events to log
  securityEvents: [
    "login_attempt",
    "login_success",
    "login_failure",
    "logout",
    "password_change",
    "password_reset_request",
    "account_locked",
    "suspicious_activity",
    "rate_limit_exceeded",
    "unauthorized_access",
  ],
};

// Environment-specific overrides
export const getSecurityConfig = (environment = "production") => {
  const baseConfig = {
    csp: cspConfig,
    headers: securityHeaders,
    rateLimit: rateLimitConfig,
    cors: corsConfig,
    validation: validationRules,
    session: sessionConfig,
    logging: loggingConfig,
  };

  // Development overrides
  if (environment === "development") {
    baseConfig.csp.reportOnly = true;
    baseConfig.cors.origin = ["http://localhost:8080", "http://localhost:3000"];
    baseConfig.logging.level = "debug";
    baseConfig.logging.logSensitiveData = true;
  }

  // Staging overrides
  if (environment === "staging") {
    baseConfig.cors.origin.push("https://staging.kayafinance.com");
    baseConfig.logging.level = "info";
  }

  return baseConfig;
};

// Security utilities
export const securityUtils = {
  // Generate secure random string
  generateSecureToken: (length = 32) => {
    const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    let result = "";
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  },
  
  // Sanitize input
  sanitizeInput: (input) => {
    if (typeof input !== "string") return input;
    return input
      .replace(/[<>]/g, "") // Remove potential HTML tags
      .replace(/javascript:/gi, "") // Remove javascript: protocol
      .replace(/on\w+=/gi, "") // Remove event handlers
      .trim();
  },
  
  // Validate file type
  validateFileType: (file, allowedTypes = validationRules.fileUpload.allowedTypes) => {
    return allowedTypes.includes(file.type);
  },
  
  // Check if IP is in whitelist (for admin functions)
  isWhitelistedIP: (ip, whitelist = []) => {
    return whitelist.includes(ip);
  },
};

export default getSecurityConfig;
