import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { createHash, createHmac } from 'https://deno.land/std@0.168.0/node/crypto.ts'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface WebhookEvent {
  type: string
  data: Record<string, unknown>
  timestamp: string
  signature?: string
}

interface BounceEvent {
  messageId: string
  email: string
  bounceType: 'hard' | 'soft'
  bounceSubType?: string
  reason?: string
  diagnosticCode?: string
  timestamp: string
}

interface ComplaintEvent {
  messageId: string
  email: string
  complaintType?: string
  timestamp: string
  feedbackId?: string
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const webhookSecret = Deno.env.get('EMAIL_BOUNCE_WEBHOOK_SECRET')
    
    const supabase = createClient(supabaseUrl, supabaseKey)

    // Get request body and headers
    const body = await req.text()
    const signature = req.headers.get('x-webhook-signature') || req.headers.get('signature')
    const provider = req.headers.get('x-email-provider') || detectProvider(req.headers)

    // Verify webhook signature if secret is configured
    if (webhookSecret && signature) {
      const isValid = verifyWebhookSignature(body, signature, webhookSecret, provider)
      if (!isValid) {
        return new Response(
          JSON.stringify({ error: 'Invalid webhook signature' }),
          { 
            status: 401, 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        )
      }
    }

    // Parse webhook payload
    const payload = JSON.parse(body)
    console.log('Received webhook payload:', JSON.stringify(payload, null, 2))

    // Process webhook based on provider
    let processedEvents: any[] = []
    
    switch (provider) {
      case 'resend':
        processedEvents = await processResendWebhook(payload, supabase)
        break
      case 'sendgrid':
        processedEvents = await processSendGridWebhook(payload, supabase)
        break
      case 'aws-ses':
        processedEvents = await processAWSSESWebhook(payload, supabase)
        break
      default:
        console.warn(`Unknown email provider: ${provider}`)
        processedEvents = await processGenericWebhook(payload, supabase)
    }

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Webhook processed successfully',
        events_processed: processedEvents.length,
        events: processedEvents
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error) {
    console.error('Webhook processing error:', error)
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})

/**
 * Detect email provider from headers
 */
function detectProvider(headers: Headers): string {
  const userAgent = headers.get('user-agent') || ''
  const contentType = headers.get('content-type') || ''
  
  if (userAgent.includes('Resend') || headers.get('resend-webhook-id')) {
    return 'resend'
  }
  
  if (userAgent.includes('SendGrid') || headers.get('x-twilio-email-event-webhook-signature')) {
    return 'sendgrid'
  }
  
  if (userAgent.includes('Amazon') || headers.get('x-amz-sns-message-type')) {
    return 'aws-ses'
  }
  
  return 'generic'
}

/**
 * Verify webhook signature
 */
function verifyWebhookSignature(
  body: string, 
  signature: string, 
  secret: string, 
  provider: string
): boolean {
  try {
    switch (provider) {
      case 'resend':
        // Resend uses HMAC-SHA256
        const expectedSignature = createHmac('sha256', secret).update(body).digest('hex')
        return signature === expectedSignature
      
      case 'sendgrid':
        // SendGrid uses ECDSA signature
        // For simplicity, we'll use HMAC verification
        const sgExpected = createHmac('sha256', secret).update(body).digest('base64')
        return signature === sgExpected
      
      case 'aws-ses':
        // AWS SNS signature verification would be more complex
        // For now, return true if secret matches
        return true
      
      default:
        // Generic HMAC verification
        const genericExpected = createHmac('sha256', secret).update(body).digest('hex')
        return signature === genericExpected
    }
  } catch (error) {
    console.error('Signature verification error:', error)
    return false
  }
}

/**
 * Process Resend webhook events
 */
async function processResendWebhook(payload: any, supabase: any): Promise<any[]> {
  const events = Array.isArray(payload) ? payload : [payload]
  const processedEvents = []

  for (const event of events) {
    try {
      const eventType = event.type
      const eventData = event.data
      
      // Find delivery record by external ID
      const { data: delivery } = await supabase
        .from('email_deliveries')
        .select('*')
        .eq('external_id', eventData.email_id)
        .single()

      if (!delivery) {
        console.warn(`Delivery not found for email_id: ${eventData.email_id}`)
        continue
      }

      // Process different event types
      switch (eventType) {
        case 'email.bounced':
          await processBounceEvent(delivery.id, {
            messageId: eventData.email_id,
            email: eventData.to,
            bounceType: eventData.bounce_type === 'hard' ? 'hard' : 'soft',
            reason: eventData.reason,
            timestamp: eventData.created_at
          }, supabase)
          break

        case 'email.complained':
          await processComplaintEvent(delivery.id, {
            messageId: eventData.email_id,
            email: eventData.to,
            timestamp: eventData.created_at
          }, supabase)
          break

        case 'email.delivered':
          await updateDeliveryStatus(delivery.id, 'delivered', eventData.created_at, supabase)
          break

        case 'email.opened':
          await updateDeliveryStatus(delivery.id, 'opened', eventData.created_at, supabase)
          break

        case 'email.clicked':
          await updateDeliveryStatus(delivery.id, 'clicked', eventData.created_at, supabase)
          break
      }

      processedEvents.push({
        event_type: eventType,
        delivery_id: delivery.id,
        processed_at: new Date().toISOString()
      })

    } catch (error) {
      console.error('Error processing Resend event:', error)
    }
  }

  return processedEvents
}

/**
 * Process SendGrid webhook events
 */
async function processSendGridWebhook(payload: any, supabase: any): Promise<any[]> {
  const events = Array.isArray(payload) ? payload : [payload]
  const processedEvents = []

  for (const event of events) {
    try {
      // SendGrid events have different structure
      const eventType = event.event
      const messageId = event.sg_message_id
      
      // Find delivery record
      const { data: delivery } = await supabase
        .from('email_deliveries')
        .select('*')
        .eq('external_id', messageId)
        .single()

      if (!delivery) {
        console.warn(`Delivery not found for message_id: ${messageId}`)
        continue
      }

      // Process event types
      switch (eventType) {
        case 'bounce':
          await processBounceEvent(delivery.id, {
            messageId: messageId,
            email: event.email,
            bounceType: event.type === 'bounce' ? 'hard' : 'soft',
            reason: event.reason,
            timestamp: new Date(event.timestamp * 1000).toISOString()
          }, supabase)
          break

        case 'spamreport':
          await processComplaintEvent(delivery.id, {
            messageId: messageId,
            email: event.email,
            timestamp: new Date(event.timestamp * 1000).toISOString()
          }, supabase)
          break

        case 'delivered':
          await updateDeliveryStatus(delivery.id, 'delivered', new Date(event.timestamp * 1000).toISOString(), supabase)
          break

        case 'open':
          await updateDeliveryStatus(delivery.id, 'opened', new Date(event.timestamp * 1000).toISOString(), supabase)
          break

        case 'click':
          await updateDeliveryStatus(delivery.id, 'clicked', new Date(event.timestamp * 1000).toISOString(), supabase)
          break
      }

      processedEvents.push({
        event_type: eventType,
        delivery_id: delivery.id,
        processed_at: new Date().toISOString()
      })

    } catch (error) {
      console.error('Error processing SendGrid event:', error)
    }
  }

  return processedEvents
}

/**
 * Process AWS SES webhook events
 */
async function processAWSSESWebhook(payload: any, supabase: any): Promise<any[]> {
  // AWS SES sends SNS notifications
  // Implementation would depend on SNS message format
  console.log('AWS SES webhook processing not yet implemented')
  return []
}

/**
 * Process generic webhook events
 */
async function processGenericWebhook(payload: any, supabase: any): Promise<any[]> {
  console.log('Generic webhook processing - payload logged for analysis')
  return []
}

/**
 * Process bounce event
 */
async function processBounceEvent(deliveryId: string, bounceData: BounceEvent, supabase: any): Promise<void> {
  try {
    // Call the database function to process bounce
    const { error } = await supabase.rpc('process_bounce_event', {
      delivery_id_param: deliveryId,
      event_type_param: 'bounce',
      bounce_type_param: bounceData.bounceType,
      reason_param: bounceData.reason,
      raw_data_param: bounceData
    })

    if (error) {
      throw error
    }

    console.log(`Processed bounce event for delivery: ${deliveryId}`)
  } catch (error) {
    console.error('Error processing bounce event:', error)
    throw error
  }
}

/**
 * Process complaint event
 */
async function processComplaintEvent(deliveryId: string, complaintData: ComplaintEvent, supabase: any): Promise<void> {
  try {
    // Call the database function to process complaint
    const { error } = await supabase.rpc('process_bounce_event', {
      delivery_id_param: deliveryId,
      event_type_param: 'complaint',
      bounce_type_param: 'complaint',
      reason_param: 'Spam complaint',
      raw_data_param: complaintData
    })

    if (error) {
      throw error
    }

    console.log(`Processed complaint event for delivery: ${deliveryId}`)
  } catch (error) {
    console.error('Error processing complaint event:', error)
    throw error
  }
}

/**
 * Update delivery status
 */
async function updateDeliveryStatus(
  deliveryId: string,
  status: string,
  timestamp: string,
  supabase: any
): Promise<void> {
  try {
    const updateData: any = { status, updated_at: new Date().toISOString() }

    switch (status) {
      case 'delivered':
        updateData.delivered_at = timestamp
        break
      case 'opened':
        updateData.opened_at = timestamp
        break
      case 'clicked':
        updateData.clicked_at = timestamp
        break
    }

    const { error } = await supabase
      .from('email_deliveries')
      .update(updateData)
      .eq('id', deliveryId)

    if (error) {
      throw error
    }

    console.log(`Updated delivery ${deliveryId} status to: ${status}`)
  } catch (error) {
    console.error('Error updating delivery status:', error)
    throw error
  }
}
