import { useState, useEffect, useCallback } from 'react'
import { useAuth } from '@/hooks/useAuthHook'
import { supabase } from '@/lib/supabase'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { formatCurrency } from '@/lib/utils'
import { LoadingSpinner } from '@/components/ui/loading'
import { Calendar, FileText } from 'lucide-react'

interface AccountTransaction {
  id: string
  created_at: string
  debit: number
  credit: number
  description: string
  journal_entry_id: string
  journal_entry?: {
    reference: string
    description: string
    date: string
  }
}

interface AccountTransactionsModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  accountId: string
  accountName: string
  startDate: string
  endDate: string
}

export function AccountTransactionsModal({
  open,
  onOpenChange,
  accountId,
  accountName,
  startDate,
  endDate
}: AccountTransactionsModalProps) {
  const { profile } = useAuth()
  const [transactions, setTransactions] = useState<AccountTransaction[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchAccountTransactions = useCallback(async () => {
    if (!profile?.org_id || !accountId) return

    setLoading(true)
    setError(null)

    try {
      const { data, error } = await supabase
        .from('transaction_lines')
        .select(`
          *,
          journal_entry:journal_entries(
            reference,
            description,
            date
          )
        `)
        .eq('org_id', profile.org_id)
        .eq('account_id', accountId)
        .gte('created_at', `${startDate}T00:00:00`)
        .lte('created_at', `${endDate}T23:59:59`)
        .order('created_at', { ascending: false })

      if (error) throw error

      setTransactions(data || [])
    } catch (error) {
      console.error('Error fetching account transactions:', error)
      setError('Failed to load transactions')
    } finally {
      setLoading(false)
    }
  }, [profile?.org_id, accountId, startDate, endDate])

  useEffect(() => {
    if (open && accountId) {
      fetchAccountTransactions()
    }
  }, [open, accountId, startDate, endDate, fetchAccountTransactions])

  const getTotalDebits = () => {
    return transactions.reduce((sum, t) => sum + (t.debit || 0), 0)
  }

  const getTotalCredits = () => {
    return transactions.reduce((sum, t) => sum + (t.credit || 0), 0)
  }

  const getNetAmount = () => {
    return getTotalDebits() - getTotalCredits()
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-scroll">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Account Transactions: {accountName}
          </DialogTitle>
          <DialogDescription>
            View detailed transaction history for the selected account and date range.
          </DialogDescription>
          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            <div className="flex items-center gap-1">
              <Calendar className="h-4 w-4" />
              {new Date(startDate).toLocaleDateString()} - {new Date(endDate).toLocaleDateString()}
            </div>
            <div>
              {transactions.length} transaction{transactions.length !== 1 ? 's' : ''}
            </div>
          </div>
        </DialogHeader>

        {loading ? (
          <div className="py-8">
            <LoadingSpinner text="Loading transactions..." showText />
          </div>
        ) : error ? (
          <div className="text-center py-8 text-red-600">
            {error}
          </div>
        ) : (
          <div className="space-y-4">
            {/* Summary */}
            <div className="grid grid-cols-3 gap-4 p-4 bg-muted/50 rounded-lg">
              <div className="text-center">
                <p className="text-sm text-muted-foreground">Total Debits</p>
                <p className="text-lg font-semibold text-green-600">
                  {formatCurrency(getTotalDebits())}
                </p>
              </div>
              <div className="text-center">
                <p className="text-sm text-muted-foreground">Total Credits</p>
                <p className="text-lg font-semibold text-red-600">
                  {formatCurrency(getTotalCredits())}
                </p>
              </div>
              <div className="text-center">
                <p className="text-sm text-muted-foreground">Net Amount</p>
                <p className={`text-lg font-semibold ${getNetAmount() >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {formatCurrency(getNetAmount())}
                </p>
              </div>
            </div>

            {/* Transactions Table */}
            {transactions.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Reference</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead className="text-right">Debit</TableHead>
                    <TableHead className="text-right">Credit</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {transactions.map((transaction) => (
                    <TableRow key={transaction.id}>
                      <TableCell>
                        {new Date(transaction.journal_entry?.date || transaction.created_at).toLocaleDateString()}
                      </TableCell>
                      <TableCell className="font-mono text-sm">
                        {transaction.journal_entry?.reference || '-'}
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium">
                            {transaction.description || transaction.journal_entry?.description || 'No description'}
                          </p>
                          {transaction.journal_entry?.description && transaction.description && 
                           transaction.description !== transaction.journal_entry.description && (
                            <p className="text-sm text-muted-foreground">
                              {transaction.journal_entry.description}
                            </p>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        {transaction.debit > 0 && (
                          <Badge variant="outline" className="text-green-600 border-green-200">
                            {formatCurrency(transaction.debit)}
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        {transaction.credit > 0 && (
                          <Badge variant="outline" className="text-red-600 border-red-200">
                            {formatCurrency(transaction.credit)}
                          </Badge>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                No transactions found for the selected period
              </div>
            )}
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
