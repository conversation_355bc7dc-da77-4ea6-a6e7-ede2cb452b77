# Kaya Finance - Production Docker Compose Configuration
version: '3.8'

services:
  # Main application
  kaya-finance:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: kaya-finance-app
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    environment:
      - NODE_ENV=production
      - NGINX_WORKER_PROCESSES=auto
      - NGINX_WORKER_CONNECTIONS=1024
    volumes:
      # SSL certificates (if using custom certificates)
      - ./ssl:/etc/nginx/ssl:ro
      # Logs
      - ./logs/nginx:/var/log/nginx
      # Custom nginx config (if needed)
      - ./nginx-prod.conf:/etc/nginx/conf.d/default.conf:ro
    networks:
      - kaya-network
    healthcheck:
      test: ["CMD", "/usr/local/bin/health-check.sh"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.kaya-finance.rule=Host(`app.kayafinance.com`)"
      - "traefik.http.routers.kaya-finance.tls=true"
      - "traefik.http.routers.kaya-finance.tls.certresolver=letsencrypt"
      - "traefik.http.services.kaya-finance.loadbalancer.server.port=80"
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.5'
          memory: 256M
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s

  # Reverse proxy (optional - if using Traefik)
  traefik:
    image: traefik:v2.10
    container_name: kaya-traefik
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"  # Traefik dashboard
    command:
      - --api.dashboard=true
      - --api.insecure=true
      - --providers.docker=true
      - --providers.docker.exposedbydefault=false
      - --entrypoints.web.address=:80
      - --entrypoints.websecure.address=:443
      - --certificatesresolvers.letsencrypt.acme.tlschallenge=true
      - --certificatesresolvers.letsencrypt.acme.email=<EMAIL>
      - --certificatesresolvers.letsencrypt.acme.storage=/letsencrypt/acme.json
      - --log.level=INFO
      - --accesslog=true
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./letsencrypt:/letsencrypt
      - ./logs/traefik:/var/log/traefik
    networks:
      - kaya-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.traefik.rule=Host(`traefik.kayafinance.com`)"
      - "traefik.http.routers.traefik.tls=true"
      - "traefik.http.routers.traefik.tls.certresolver=letsencrypt"
    profiles:
      - traefik

  # Monitoring with Prometheus (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: kaya-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - kaya-network
    profiles:
      - monitoring

  # Grafana for monitoring dashboards (optional)
  grafana:
    image: grafana/grafana:latest
    container_name: kaya-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - kaya-network
    profiles:
      - monitoring

  # Log aggregation with Loki (optional)
  loki:
    image: grafana/loki:latest
    container_name: kaya-loki
    restart: unless-stopped
    ports:
      - "3100:3100"
    volumes:
      - ./monitoring/loki-config.yml:/etc/loki/local-config.yaml:ro
      - loki-data:/loki
    command: -config.file=/etc/loki/local-config.yaml
    networks:
      - kaya-network
    profiles:
      - monitoring

  # Log shipping with Promtail (optional)
  promtail:
    image: grafana/promtail:latest
    container_name: kaya-promtail
    restart: unless-stopped
    volumes:
      - ./logs:/var/log:ro
      - ./monitoring/promtail-config.yml:/etc/promtail/config.yml:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
    command: -config.file=/etc/promtail/config.yml
    networks:
      - kaya-network
    profiles:
      - monitoring

# Networks
networks:
  kaya-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# Volumes
volumes:
  prometheus-data:
    driver: local
  grafana-data:
    driver: local
  loki-data:
    driver: local

# Secrets (for production use)
secrets:
  ssl_cert:
    file: ./ssl/cert.pem
  ssl_key:
    file: ./ssl/key.pem
