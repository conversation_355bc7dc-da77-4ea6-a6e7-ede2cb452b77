#!/bin/bash

# Kaya Finance - Production Deployment Script
# This script handles the deployment of the application to production

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DEPLOYMENT_TARGET=${1:-"netlify"}  # Default to Netlify
BUILD_DIR="dist"
LOG_FILE="deploy-$(date +%Y%m%d-%H%M%S).log"

echo -e "${BLUE}🚀 Starting Kaya Finance Production Deployment${NC}"
echo "Deployment target: $DEPLOYMENT_TARGET"
echo "Deployment started at: $(date)"

# Function to log messages
log() {
    echo -e "$1" | tee -a "$LOG_FILE"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Pre-deployment checks
log "${YELLOW}📋 Running pre-deployment checks...${NC}"

# Check if build directory exists
if [ ! -d "$BUILD_DIR" ]; then
    log "${RED}❌ Build directory not found. Run production build first.${NC}"
    exit 1
fi

# Check if build is recent (less than 1 hour old)
if [ -d "$BUILD_DIR" ]; then
    BUILD_AGE=$(find "$BUILD_DIR" -maxdepth 1 -name "index.html" -mmin -60 | wc -l)
    if [ "$BUILD_AGE" -eq 0 ]; then
        log "${YELLOW}⚠️  Build is older than 1 hour. Consider rebuilding.${NC}"
        read -p "Continue with deployment? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log "${YELLOW}Deployment cancelled by user${NC}"
            exit 1
        fi
    fi
fi

# Function to deploy to Netlify
deploy_netlify() {
    log "${YELLOW}🌐 Deploying to Netlify...${NC}"
    
    if ! command_exists netlify; then
        log "${RED}❌ Netlify CLI not found. Installing...${NC}"
        npm install -g netlify-cli
    fi
    
    # Check if netlify.toml exists
    if [ ! -f "netlify.toml" ]; then
        log "${YELLOW}📝 Creating netlify.toml configuration...${NC}"
        cat > netlify.toml << EOF
[build]
  publish = "dist"
  command = "npm run build:production"

[build.environment]
  NODE_VERSION = "18"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-Content-Type-Options = "nosniff"
    X-XSS-Protection = "1; mode=block"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Permissions-Policy = "camera=(), microphone=(), geolocation=()"

[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.js"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.css"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"
EOF
    fi
    
    # Deploy to Netlify
    netlify deploy --prod --dir="$BUILD_DIR" 2>&1 | tee -a "$LOG_FILE"
    
    if [ $? -eq 0 ]; then
        log "${GREEN}✅ Successfully deployed to Netlify${NC}"
    else
        log "${RED}❌ Netlify deployment failed${NC}"
        exit 1
    fi
}

# Function to deploy to Vercel
deploy_vercel() {
    log "${YELLOW}▲ Deploying to Vercel...${NC}"
    
    if ! command_exists vercel; then
        log "${RED}❌ Vercel CLI not found. Installing...${NC}"
        npm install -g vercel
    fi
    
    # Check if vercel.json exists
    if [ ! -f "vercel.json" ]; then
        log "${YELLOW}📝 Creating vercel.json configuration...${NC}"
        cat > vercel.json << EOF
{
  "version": 2,
  "builds": [
    {
      "src": "package.json",
      "use": "@vercel/static-build",
      "config": {
        "distDir": "dist"
      }
    }
  ],
  "routes": [
    {
      "handle": "filesystem"
    },
    {
      "src": "/.*",
      "dest": "/index.html"
    }
  ],
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        },
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        },
        {
          "key": "X-XSS-Protection",
          "value": "1; mode=block"
        },
        {
          "key": "Referrer-Policy",
          "value": "strict-origin-when-cross-origin"
        }
      ]
    }
  ]
}
EOF
    fi
    
    # Deploy to Vercel
    vercel --prod 2>&1 | tee -a "$LOG_FILE"
    
    if [ $? -eq 0 ]; then
        log "${GREEN}✅ Successfully deployed to Vercel${NC}"
    else
        log "${RED}❌ Vercel deployment failed${NC}"
        exit 1
    fi
}

# Function to deploy to custom server
deploy_custom() {
    log "${YELLOW}🖥️  Deploying to custom server...${NC}"
    
    # This is a template - customize based on your server setup
    if [ -z "$DEPLOY_HOST" ] || [ -z "$DEPLOY_PATH" ]; then
        log "${RED}❌ DEPLOY_HOST and DEPLOY_PATH environment variables must be set${NC}"
        exit 1
    fi
    
    # Example using rsync
    if command_exists rsync; then
        rsync -avz --delete "$BUILD_DIR/" "$DEPLOY_HOST:$DEPLOY_PATH" 2>&1 | tee -a "$LOG_FILE"
    else
        log "${RED}❌ rsync not found. Please install rsync or use alternative deployment method${NC}"
        exit 1
    fi
}

# Run deployment based on target
case $DEPLOYMENT_TARGET in
    "netlify")
        deploy_netlify
        ;;
    "vercel")
        deploy_vercel
        ;;
    "custom")
        deploy_custom
        ;;
    *)
        log "${RED}❌ Unknown deployment target: $DEPLOYMENT_TARGET${NC}"
        log "Supported targets: netlify, vercel, custom"
        exit 1
        ;;
esac

# Post-deployment checks
log "${YELLOW}🔍 Running post-deployment checks...${NC}"

# Wait a moment for deployment to propagate
sleep 10

# Health check (if URL is provided)
if [ ! -z "$HEALTH_CHECK_URL" ]; then
    log "${YELLOW}🏥 Running health check...${NC}"
    
    if command_exists curl; then
        HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$HEALTH_CHECK_URL")
        if [ "$HTTP_STATUS" -eq 200 ]; then
            log "${GREEN}✅ Health check passed (HTTP $HTTP_STATUS)${NC}"
        else
            log "${RED}❌ Health check failed (HTTP $HTTP_STATUS)${NC}"
            exit 1
        fi
    else
        log "${YELLOW}⚠️  curl not found, skipping health check${NC}"
    fi
fi

# Success message
log "${GREEN}✅ Production deployment completed successfully!${NC}"
log "Deployment completed at: $(date)"

echo ""
echo -e "${GREEN}🎉 Deployment successful!${NC}"
echo ""
echo "Next steps:"
echo "1. Verify the application is working correctly"
echo "2. Run smoke tests on the deployed application"
echo "3. Monitor application performance and errors"
echo "4. Update DNS records if necessary"
echo ""
echo "Deployment log saved to: $LOG_FILE"
