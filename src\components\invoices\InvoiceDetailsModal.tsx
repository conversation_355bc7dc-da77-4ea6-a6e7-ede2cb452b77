import { useState, useEffect, useCallback } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import {
  FileText,
  User,
  Calendar,
  DollarSign,
  Phone,
  Mail,
  MapPin,
  Building,
  Printer,
  Download
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuthHook'
import { useInvoicePdfExport } from '@/hooks/useInvoicePdfExport'
import { LoadingSpinner } from '@/components/ui/loading'
import type { InvoiceWithCustomer } from '@/types/invoices'
import type { Customer } from '@/types/database'
import { formatCurrency } from '@/lib/utils'

interface InvoiceDetailsModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  invoiceId: string
}

interface InvoiceLineWithAccount {
  id: string
  account_id: string
  description: string
  quantity: number
  unit_price: number
  tax_rate_pct: number
  line_total: number
  tax_amount: number
  accounts?: {
    code: string
    name: string
  }
}

interface InvoiceDetails extends InvoiceWithCustomer {
  invoice_lines?: InvoiceLineWithAccount[]
}

export function InvoiceDetailsModal({ 
  open, 
  onOpenChange, 
  invoiceId 
}: InvoiceDetailsModalProps) {
  const { profile } = useAuth()
  const { exportToPdf, printInvoice, isExporting } = useInvoicePdfExport()
  const [invoice, setInvoice] = useState<InvoiceDetails | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchInvoiceDetails = useCallback(async () => {
    setLoading(true)
    setError(null)
    
    try {
      // Fetch invoice details with customer
      const { data: invoiceData, error: invoiceError } = await supabase
        .from('invoices')
        .select(`
          *,
          customers(*)
        `)
        .eq('id', invoiceId)
        .single()

      if (invoiceError) throw invoiceError

      // Fetch invoice lines with account details
      const { data: linesData, error: linesError } = await supabase
        .from('invoice_lines')
        .select(`
          *,
          accounts(code, name)
        `)
        .eq('invoice_id', invoiceId)
        .order('created_at')

      if (linesError) throw linesError

      // Calculate line totals
      const enhancedLines = linesData?.map(line => ({
        ...line,
        line_total: line.quantity * line.unit_price,
        tax_amount: (line.quantity * line.unit_price) * (line.tax_rate_pct / 100)
      })) || []

      const enhancedInvoice: InvoiceDetails = {
        ...invoiceData,
        invoice_lines: enhancedLines
      }

      setInvoice(enhancedInvoice)

    } catch (error) {
      console.error('Error fetching invoice details:', error)
      setError('Failed to load invoice details')
    } finally {
      setLoading(false)
    }
  }, [invoiceId])

  useEffect(() => {
    if (open && invoiceId) {
      fetchInvoiceDetails()
    }
  }, [open, invoiceId, fetchInvoiceDetails])

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'paid':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'sent':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case 'overdue':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      case 'draft':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
      case 'cancelled':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
    }
  }

  if (loading) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-scroll">
          <LoadingSpinner size="lg" text="Loading invoice details..." showText className="h-64" />
        </DialogContent>
      </Dialog>
    )
  }

  if (error || !invoice) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-4xl">
          <div className="text-center py-8">
            <p className="text-destructive">{error || 'Invoice not found'}</p>
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  const subtotal = invoice.invoice_lines?.reduce((sum, line) => sum + line.line_total, 0) || 0
  const totalTax = invoice.invoice_lines?.reduce((sum, line) => sum + line.tax_amount, 0) || 0

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-scroll">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Invoice Details - {invoice.invoice_number}
            </DialogTitle>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => printInvoice(invoice)}
                disabled={isExporting}
              >
                <Printer className="h-4 w-4 mr-2" />
                Print
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => exportToPdf(invoice)}
                disabled={isExporting}
              >
                {isExporting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2" />
                    Exporting...
                  </>
                ) : (
                  <>
                    <Download className="h-4 w-4 mr-2" />
                    Export PDF
                  </>
                )}
              </Button>
            </div>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* Invoice Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Invoice Overview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div>
                  <Label className="text-muted-foreground">Total Amount</Label>
                  <p className="text-2xl font-bold text-primary">
                    {formatCurrency(invoice.total_amount)}
                  </p>
                </div>
                <div>
                  <Label className="text-muted-foreground">Status</Label>
                  <Badge className={getStatusColor(invoice.status)}>
                    {invoice.status}
                  </Badge>
                </div>
                <div>
                  <Label className="text-muted-foreground">Date Issued</Label>
                  <p className="font-medium">
                    {new Date(invoice.date_issued).toLocaleDateString('en-UG', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </p>
                </div>
                <div>
                  <Label className="text-muted-foreground">Due Date</Label>
                  <p className="font-medium">
                    {new Date(invoice.due_date).toLocaleDateString('en-UG', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Customer Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  Customer Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <Label className="text-muted-foreground">Name</Label>
                  <p className="font-medium">{invoice.customers?.name || 'Unknown Customer'}</p>
                </div>
                {invoice.customers?.email && (
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{invoice.customers.email}</span>
                  </div>
                )}
                {invoice.customers?.phone && (
                  <div className="flex items-center gap-2">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{invoice.customers.phone}</span>
                  </div>
                )}
                {invoice.customers?.address && (
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{invoice.customers.address}</span>
                  </div>
                )}
                {invoice.customers?.payment_terms && (
                  <div>
                    <Label className="text-muted-foreground">Payment Terms</Label>
                    <p className="text-sm">{invoice.customers.payment_terms} days</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Invoice Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building className="h-4 w-4" />
                  Invoice Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <Label className="text-muted-foreground">Invoice Number</Label>
                  <p className="font-mono text-sm bg-muted p-2 rounded">
                    {invoice.invoice_number}
                  </p>
                </div>
                <div>
                  <Label className="text-muted-foreground">Tax Amount</Label>
                  <p className="font-medium">{formatCurrency(invoice.tax_amount || 0)}</p>
                </div>
                <div>
                  <Label className="text-muted-foreground">Created</Label>
                  <p className="text-sm">{new Date(invoice.created_at).toLocaleString()}</p>
                </div>
                {invoice.updated_at && (
                  <div>
                    <Label className="text-muted-foreground">Last Updated</Label>
                    <p className="text-sm">{new Date(invoice.updated_at).toLocaleString()}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Invoice Lines */}
          {invoice.invoice_lines && invoice.invoice_lines.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Invoice Line Items</CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Account</TableHead>
                      <TableHead>Description</TableHead>
                      <TableHead className="text-right">Qty</TableHead>
                      <TableHead className="text-right">Unit Price</TableHead>
                      <TableHead className="text-right">Tax %</TableHead>
                      <TableHead className="text-right">Line Total</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {invoice.invoice_lines.map((line) => (
                      <TableRow key={line.id}>
                        <TableCell>
                          <div>
                            <p className="font-medium">{line.accounts?.name || 'Unknown Account'}</p>
                            <p className="text-sm text-muted-foreground">{line.accounts?.code}</p>
                          </div>
                        </TableCell>
                        <TableCell>{line.description}</TableCell>
                        <TableCell className="text-right">{line.quantity}</TableCell>
                        <TableCell className="text-right">{formatCurrency(line.unit_price)}</TableCell>
                        <TableCell className="text-right">{line.tax_rate_pct}%</TableCell>
                        <TableCell className="text-right font-medium">
                          {formatCurrency(line.line_total)}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>

                {/* Totals Summary */}
                <div className="mt-4 p-4 bg-muted rounded-lg">
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Subtotal:</span>
                      <span>{formatCurrency(subtotal)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Tax:</span>
                      <span>{formatCurrency(totalTax)}</span>
                    </div>
                    <div className="flex justify-between font-bold border-t pt-2">
                      <span>Total:</span>
                      <span>{formatCurrency(invoice.total_amount)}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Notes */}
          {invoice.notes && (
            <Card>
              <CardHeader>
                <CardTitle>Notes</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm whitespace-pre-wrap">{invoice.notes}</p>
              </CardContent>
            </Card>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
