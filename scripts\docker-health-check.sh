#!/bin/sh

# Kaya Finance - Docker Health Check Script
# This script performs health checks for the containerized application

set -e

# Configuration
HEALTH_URL="http://localhost:80/health"
TIMEOUT=10
MAX_RETRIES=3

# Colors for output (if supported)
if [ -t 1 ]; then
    RED='\033[0;31m'
    GREEN='\033[0;32m'
    YELLOW='\033[1;33m'
    NC='\033[0m'
else
    RED=''
    GREEN=''
    YELLOW=''
    NC=''
fi

# Logging function
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# Health check function
check_health() {
    local attempt=1
    
    while [ $attempt -le $MAX_RETRIES ]; do
        log "Health check attempt $attempt/$MAX_RETRIES"
        
        # Check if nginx is running
        if ! pgrep nginx > /dev/null; then
            log "${RED}ERROR: Nginx process not found${NC}"
            return 1
        fi
        
        # Check HTTP endpoint
        if command -v curl > /dev/null; then
            response=$(curl -s -o /dev/null -w "%{http_code}" --max-time $TIMEOUT "$HEALTH_URL" 2>/dev/null || echo "000")
        elif command -v wget > /dev/null; then
            response=$(wget -q -O /dev/null -T $TIMEOUT --server-response "$HEALTH_URL" 2>&1 | grep "HTTP/" | tail -1 | awk '{print $2}' || echo "000")
        else
            log "${RED}ERROR: Neither curl nor wget available${NC}"
            return 1
        fi
        
        case $response in
            200)
                log "${GREEN}SUCCESS: Health check passed (HTTP $response)${NC}"
                return 0
                ;;
            000)
                log "${YELLOW}WARNING: Connection failed (attempt $attempt/$MAX_RETRIES)${NC}"
                ;;
            *)
                log "${YELLOW}WARNING: Unexpected response HTTP $response (attempt $attempt/$MAX_RETRIES)${NC}"
                ;;
        esac
        
        if [ $attempt -lt $MAX_RETRIES ]; then
            sleep 2
        fi
        
        attempt=$((attempt + 1))
    done
    
    log "${RED}ERROR: Health check failed after $MAX_RETRIES attempts${NC}"
    return 1
}

# Additional checks
check_disk_space() {
    # Check if disk usage is below 90%
    disk_usage=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
    if [ "$disk_usage" -gt 90 ]; then
        log "${YELLOW}WARNING: Disk usage is ${disk_usage}%${NC}"
        return 1
    fi
    return 0
}

check_memory() {
    # Check if memory usage is reasonable
    if [ -f /proc/meminfo ]; then
        mem_total=$(grep MemTotal /proc/meminfo | awk '{print $2}')
        mem_available=$(grep MemAvailable /proc/meminfo | awk '{print $2}')
        mem_usage=$((100 - (mem_available * 100 / mem_total)))
        
        if [ "$mem_usage" -gt 90 ]; then
            log "${YELLOW}WARNING: Memory usage is ${mem_usage}%${NC}"
            return 1
        fi
    fi
    return 0
}

# Main health check
main() {
    log "Starting Docker health check for Kaya Finance"
    
    # Primary health check
    if ! check_health; then
        exit 1
    fi
    
    # Additional system checks
    check_disk_space || log "${YELLOW}Disk space warning${NC}"
    check_memory || log "${YELLOW}Memory usage warning${NC}"
    
    log "${GREEN}All health checks passed${NC}"
    exit 0
}

# Run main function
main "$@"
