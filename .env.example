# Kaya Finance - Environment Configuration Template
# Copy this file to .env.local for development or .env.production for production

# =====================================================
# APPLICATION CONFIGURATION
# =====================================================
VITE_APP_NAME="Kaya Finance"
VITE_APP_VERSION="1.0.0"
VITE_APP_DESCRIPTION="Modern Financial Management for SMEs"
VITE_APP_URL="https://your-domain.com"

# Environment (development, staging, production)
VITE_NODE_ENV="production"
VITE_BUILD_MODE="production"

# =====================================================
# SUPABASE CONFIGURATION
# =====================================================
VITE_SUPABASE_URL="https://your-project.supabase.co"
VITE_SUPABASE_ANON_KEY="your-anon-key-here"

# Service role key (server-side only, never expose to client)
SUPABASE_SERVICE_ROLE_KEY="your-service-role-key-here"

# Database configuration
VITE_SUPABASE_DB_URL="postgresql://postgres:[password]@db.your-project.supabase.co:5432/postgres"

# =====================================================
# AUTHENTICATION CONFIGURATION
# =====================================================
VITE_AUTH_REDIRECT_URL="https://your-domain.com/auth/callback"
VITE_AUTH_SITE_URL="https://your-domain.com"

# JWT Configuration
VITE_JWT_SECRET="your-jwt-secret-here"
VITE_JWT_EXPIRY="24h"

# =====================================================
# SECURITY CONFIGURATION
# =====================================================
# CORS Origins (comma-separated)
VITE_CORS_ORIGINS="https://your-domain.com,https://www.your-domain.com"

# Content Security Policy
VITE_CSP_ENABLED="true"

# Rate limiting
VITE_RATE_LIMIT_ENABLED="true"
VITE_RATE_LIMIT_MAX_REQUESTS="100"
VITE_RATE_LIMIT_WINDOW_MS="900000"

# =====================================================
# MONITORING & ANALYTICS
# =====================================================
# Sentry Configuration
VITE_SENTRY_DSN="https://<EMAIL>/project-id"
VITE_SENTRY_ENVIRONMENT="production"
VITE_SENTRY_RELEASE="1.0.0"

# Analytics
VITE_ANALYTICS_ENABLED="true"
VITE_ANALYTICS_ID="your-analytics-id"

# Performance monitoring
VITE_PERFORMANCE_MONITORING="true"

# =====================================================
# EMAIL CONFIGURATION
# =====================================================

# Primary Email Service Provider (choose one)
# Resend API Configuration (Recommended)
RESEND_API_KEY="re_your-api-key-here"
VITE_EMAIL_PROVIDER="resend"

# Alternative: SendGrid Configuration
SENDGRID_API_KEY="SG.your-sendgrid-api-key-here"
SENDGRID_FROM_EMAIL="<EMAIL>"
SENDGRID_FROM_NAME="KAYA Finance"

# Alternative: AWS SES Configuration
AWS_SES_REGION="us-east-1"
AWS_SES_ACCESS_KEY_ID="your-aws-access-key"
AWS_SES_SECRET_ACCESS_KEY="your-aws-secret-key"
AWS_SES_FROM_EMAIL="<EMAIL>"

# Email Sender Configuration
VITE_FROM_EMAIL="<EMAIL>"
VITE_FROM_NAME="KAYA Finance"
VITE_SUPPORT_EMAIL="<EMAIL>"
VITE_REPLY_TO_EMAIL="<EMAIL>"

# Domain and Branding
VITE_COMPANY_NAME="KAYA Finance"
VITE_COMPANY_TAGLINE="Modern Financial Management for SMEs"
VITE_COMPANY_LOGO_URL="https://your-domain.com/logo.png"
VITE_COMPANY_WEBSITE="https://your-domain.com"
VITE_COMPANY_PRIMARY_COLOR="#2563eb"

# Email Rate Limiting
EMAIL_RATE_LIMIT_PER_MINUTE="10"
EMAIL_RATE_LIMIT_PER_HOUR="100"
EMAIL_RATE_LIMIT_PER_DAY="1000"
EMAIL_BURST_LIMIT="5"

# Email Bounce Handling
EMAIL_BOUNCE_WEBHOOK_SECRET="your-webhook-secret-here"
EMAIL_MAX_BOUNCE_COUNT="3"
EMAIL_BOUNCE_COOLDOWN_HOURS="24"

# Email Retry Configuration
EMAIL_MAX_RETRIES="3"
EMAIL_RETRY_DELAY_MINUTES="5"
EMAIL_RETRY_BACKOFF_MULTIPLIER="2"

# Email Monitoring
EMAIL_MONITORING_ENABLED="true"
EMAIL_ALERT_THRESHOLD_BOUNCE_RATE="5"
EMAIL_ALERT_THRESHOLD_FAILURE_RATE="10"
EMAIL_ALERT_EMAIL="<EMAIL>"

# =====================================================
# PAYMENT CONFIGURATION
# =====================================================
# Payment gateway configuration (if applicable)
VITE_PAYMENT_GATEWAY_ENABLED="false"
VITE_PAYMENT_PUBLIC_KEY="pk_your-public-key"
PAYMENT_SECRET_KEY="sk_your-secret-key"

# =====================================================
# FILE STORAGE CONFIGURATION
# =====================================================
# Supabase Storage
VITE_STORAGE_BUCKET="kaya-finance-files"
VITE_MAX_FILE_SIZE="10485760"  # 10MB in bytes
VITE_ALLOWED_FILE_TYPES="image/*,application/pdf,text/csv"

# =====================================================
# FEATURE FLAGS
# =====================================================
VITE_FEATURE_INVENTORY_MANAGEMENT="true"
VITE_FEATURE_PURCHASE_ORDERS="true"
VITE_FEATURE_BUDGET_ENFORCEMENT="true"
VITE_FEATURE_AUDIT_LOGGING="true"
VITE_FEATURE_MULTI_CURRENCY="false"
VITE_FEATURE_ADVANCED_REPORTING="true"

# =====================================================
# LOCALIZATION
# =====================================================
VITE_DEFAULT_LOCALE="en-UG"
VITE_DEFAULT_CURRENCY="UGX"
VITE_DEFAULT_TIMEZONE="Africa/Kampala"

# =====================================================
# CACHE CONFIGURATION
# =====================================================
VITE_CACHE_ENABLED="true"
VITE_CACHE_TTL="300000"  # 5 minutes in milliseconds

# =====================================================
# LOGGING CONFIGURATION
# =====================================================
VITE_LOG_LEVEL="error"  # error, warn, info, debug
VITE_LOG_TO_CONSOLE="false"
VITE_LOG_TO_SENTRY="true"

# =====================================================
# BACKUP CONFIGURATION
# =====================================================
VITE_BACKUP_ENABLED="true"
VITE_BACKUP_RETENTION_DAYS="30"

# =====================================================
# DEVELOPMENT ONLY
# =====================================================
# These should be false in production
VITE_DEBUG_MODE="false"
VITE_REACT_QUERY_DEVTOOLS="false"
VITE_SHOW_PERFORMANCE_METRICS="false"

# =====================================================
# DEPLOYMENT CONFIGURATION
# =====================================================
# Build configuration
VITE_BUILD_OPTIMIZATION="true"
VITE_BUNDLE_ANALYZER="false"
VITE_SOURCE_MAPS="false"

# CDN Configuration
VITE_CDN_URL=""
VITE_STATIC_ASSETS_URL=""

# Health check endpoints
VITE_HEALTH_CHECK_ENABLED="true"
VITE_HEALTH_CHECK_ENDPOINT="/api/health"
