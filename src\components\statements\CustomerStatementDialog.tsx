import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { StatementOfAccount } from './StatementOfAccount'
import type { Customer } from '@/types/database'

interface CustomerStatementDialogProps {
  entity: Customer
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function CustomerStatementDialog({ 
  entity, 
  open, 
  onOpenChange 
}: CustomerStatementDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-7xl max-h-[90vh] overflow-y-scroll">
        <DialogHeader>
          <DialogTitle>Customer Statement - {entity.name}</DialogTitle>
        </DialogHeader>
        <StatementOfAccount 
          entity={entity} 
          entity_type="customer" 
        />
      </DialogContent>
    </Dialog>
  )
}
