import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { Resend } from 'https://esm.sh/resend@4.0.1'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface EmailRequest {
  type: 'user_invitation' | 'password_reset' | 'welcome' | 'notification' | 'test'
  to: string
  data: Record<string, unknown>
  org_id?: string
  priority?: 'low' | 'normal' | 'high'
  scheduled_at?: string
}

interface InvitationEmailData {
  inviter_name: string
  organization_name: string
  role: string
  invitation_url: string
  expires_at: string
}

interface EmailProvider {
  name: string
  send: (emailData: EmailSendData) => Promise<{ id?: string; error?: string }>
}

interface EmailSendData {
  from: string
  to: string[]
  subject: string
  html: string
  text: string
  replyTo?: string
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize clients
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!

    const supabase = createClient(supabaseUrl, supabaseKey)

    // Get email provider configuration
    const emailProvider = getEmailProvider()

    if (!emailProvider) {
      throw new Error('No email provider configured')
    }

    // Parse request
    const { type, to, data, org_id, priority }: EmailRequest = await req.json()

    // Validate email address
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(to)) {
      throw new Error('Invalid email address')
    }

    // Check if email is suppressed
    const { data: isSupressed, error: suppressionError } = await supabase.rpc('is_email_suppressed', {
      email_address: to
    })

    if (suppressionError) {
      console.warn('Error checking email suppression:', suppressionError)
    } else if (isSupressed) {
      throw new Error('Email address is suppressed due to previous bounces or complaints')
    }

    // Check rate limits
    const rateLimitIdentifier = org_id || `ip_${req.headers.get('x-forwarded-for') || 'unknown'}`
    const { data: rateLimitConfig } = await supabase.rpc('get_rate_limit_config', {
      org_id_param: org_id
    })

    // For now, we'll implement basic rate limiting check
    // In a production environment, you'd want to implement proper distributed rate limiting
    const currentHour = new Date().getHours()
    const { data: currentHourStats } = await supabase
      .from('email_sending_stats')
      .select('emails_sent')
      .eq('org_id', org_id)
      .eq('date', new Date().toISOString().split('T')[0])
      .eq('hour', currentHour)
      .single()

    const currentHourCount = currentHourStats?.emails_sent || 0
    const hourlyLimit = rateLimitConfig?.per_hour || 100

    if (currentHourCount >= hourlyLimit) {
      // Record rate limit violation
      await supabase.rpc('record_email_stat', {
        org_id_param: org_id,
        stat_type: 'rate_limited'
      })

      throw new Error(`Hourly email limit exceeded (${hourlyLimit} emails per hour)`)
    }

    // Get organization settings if org_id provided
    let orgSettings = null
    if (org_id) {
      const { data: org, error: orgError } = await supabase
        .from('organizations')
        .select('name, email_settings')
        .eq('id', org_id)
        .single()

      if (orgError) {
        console.error('Error fetching organization:', orgError)
      } else {
        orgSettings = org
      }
    }

    // Default email settings
    const defaultSettings = {
      from_name: 'Kaya Finance',
      from_email: '<EMAIL>',
      reply_to: '<EMAIL>'
    }

    const emailSettings = orgSettings?.email_settings || defaultSettings
    const fromEmail = `${emailSettings.from_name} <${emailSettings.from_email}>`

    let emailContent: { subject: string; html: string; text: string }

    switch (type) {
      case 'user_invitation':
        emailContent = generateInvitationEmail(data as InvitationEmailData, orgSettings?.name)
        break
      
      case 'password_reset':
        emailContent = generatePasswordResetEmail(data)
        break
      
      case 'welcome':
        emailContent = generateWelcomeEmail(data, orgSettings?.name)
        break
      
      case 'notification':
        emailContent = generateNotificationEmail(data)
        break

      case 'test':
        emailContent = generateTestEmail(data)
        break

      default:
        throw new Error(`Unsupported email type: ${type}`)
    }

    // Send email via configured provider
    const emailResult = await emailProvider.send({
      from: fromEmail,
      to: [to],
      subject: emailContent.subject,
      html: emailContent.html,
      text: emailContent.text,
      replyTo: emailSettings.reply_to,
    })

    if (emailResult.error) {
      throw new Error(`Failed to send email: ${emailResult.error}`)
    }

    // Log email activity and update statistics
    if (org_id) {
      // Log to email_deliveries table
      await supabase
        .from('email_deliveries')
        .insert({
          id: emailResult.id || `email_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          email: to,
          status: 'sent',
          sent_at: new Date().toISOString(),
          org_id,
          template_type: type,
          recipient_data: { email: to },
          external_id: emailResult.id
        })
        .catch(error => console.error('Failed to log email delivery:', error))

      // Update sending statistics
      await supabase.rpc('record_email_stat', {
        org_id_param: org_id,
        stat_type: 'sent'
      }).catch(error => console.error('Failed to record email stat:', error))

      // Check for rate limit alerts
      const newHourCount = currentHourCount + 1
      await supabase.rpc('check_rate_limit_alert', {
        org_id_param: org_id,
        current_usage: newHourCount,
        limit_value: hourlyLimit,
        time_window: 'hour'
      }).catch(error => console.error('Failed to check rate limit alert:', error))
    }

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Email sent successfully',
        email_id: emailResult.id
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error) {
    console.error('Email service error:', error)
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})

function generateInvitationEmail(data: InvitationEmailData, orgName?: string): { subject: string; html: string; text: string } {
  const organizationName = orgName || 'Kaya Finance'
  
  const subject = `You're invited to join ${organizationName}`
  
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Invitation to ${organizationName}</title>
        <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #2563eb; color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background: #f8fafc; padding: 30px; border-radius: 0 0 8px 8px; }
            .button { display: inline-block; background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
            .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
            .role-badge { background: #dbeafe; color: #1e40af; padding: 4px 12px; border-radius: 20px; font-size: 14px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>You're Invited!</h1>
                <p>Join ${organizationName} on Kaya Finance</p>
            </div>
            <div class="content">
                <p>Hello!</p>
                <p><strong>${data.inviter_name}</strong> has invited you to join <strong>${organizationName}</strong> as a <span class="role-badge">${data.role}</span>.</p>
                
                <p>Kaya Finance is a comprehensive financial management platform that helps businesses track invoices, manage expenses, and maintain accurate financial records.</p>
                
                <div style="text-align: center; margin: 30px 0;">
                    <a href="${data.invitation_url}" class="button">Accept Invitation</a>
                </div>
                
                <p><strong>What you can do with your ${data.role} role:</strong></p>
                <ul>
                    ${getRolePermissions(data.role)}
                </ul>
                
                <p><strong>Important:</strong> This invitation expires on ${new Date(data.expires_at).toLocaleDateString('en-US', { 
                    weekday: 'long', 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric' 
                })}.</p>
                
                <p>If you have any questions, please contact your administrator or reach out to our support team.</p>
            </div>
            <div class="footer">
                <p>This invitation was sent by ${organizationName} via Kaya Finance</p>
                <p>If you didn't expect this invitation, you can safely ignore this email.</p>
            </div>
        </div>
    </body>
    </html>
  `
  
  const text = `
You're invited to join ${organizationName}!

${data.inviter_name} has invited you to join ${organizationName} as a ${data.role}.

Accept your invitation: ${data.invitation_url}

This invitation expires on ${new Date(data.expires_at).toLocaleDateString()}.

If you have any questions, please contact your administrator.

---
This invitation was sent by ${organizationName} via Kaya Finance
  `
  
  return { subject, html, text }
}

function getRolePermissions(role: string): string {
  switch (role.toLowerCase()) {
    case 'admin':
      return `
        <li>Full access to all financial data and settings</li>
        <li>Manage users and permissions</li>
        <li>Configure organization settings</li>
        <li>Access all reports and analytics</li>
      `
    case 'accountant':
      return `
        <li>Create and manage invoices and bills</li>
        <li>Record payments and transactions</li>
        <li>Generate financial reports</li>
        <li>Manage customers and vendors</li>
      `
    case 'viewer':
      return `
        <li>View financial reports and dashboards</li>
        <li>Access read-only transaction data</li>
        <li>Export reports for analysis</li>
      `
    default:
      return '<li>Access to assigned features and data</li>'
  }
}

function generatePasswordResetEmail(data: Record<string, unknown>): { subject: string; html: string; text: string } {
  const resetUrl = data.reset_url as string
  const userEmail = data.user_email as string
  const expiresAt = data.expires_at as string

  const subject = 'Reset your KAYA Finance password'

  const html = `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Reset Your Password - KAYA Finance</title>
        <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f8fafc; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; }
            .header { background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); color: white; padding: 40px 30px; text-align: center; }
            .logo { font-size: 28px; font-weight: bold; margin-bottom: 10px; }
            .content { padding: 40px 30px; }
            .security-notice { background: #fef3c7; border: 1px solid #f59e0b; border-radius: 8px; padding: 20px; margin: 20px 0; }
            .button { display: inline-block; background: #2563eb; color: white; padding: 16px 32px; text-decoration: none; border-radius: 8px; margin: 30px 0; font-weight: 600; }
            .button:hover { background: #1d4ed8; }
            .footer { background: #f8fafc; padding: 30px; text-align: center; color: #666; font-size: 14px; border-top: 1px solid #e5e7eb; }
            .expiry-info { background: #fee2e2; border: 1px solid #fca5a5; border-radius: 8px; padding: 15px; margin: 20px 0; color: #991b1b; }
            .help-section { background: #f0f9ff; border: 1px solid #7dd3fc; border-radius: 8px; padding: 20px; margin: 20px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="logo">🏦 KAYA Finance</div>
                <p style="margin: 0; opacity: 0.9;">Modern Financial Management for SMEs</p>
            </div>
            <div class="content">
                <h1 style="color: #1f2937; margin-bottom: 20px;">Reset Your Password</h1>
                <p>Hello,</p>
                <p>We received a request to reset the password for your KAYA Finance account (<strong>${userEmail}</strong>).</p>

                <div class="security-notice">
                    <strong>🔒 Security Notice:</strong> If you didn't request this password reset, please ignore this email. Your account remains secure.
                </div>

                <p>To reset your password, click the button below:</p>

                <div style="text-align: center;">
                    <a href="${resetUrl}" class="button">Reset My Password</a>
                </div>

                <div class="expiry-info">
                    <strong>⏰ Important:</strong> This password reset link expires on ${new Date(expiresAt).toLocaleDateString('en-US', {
                        weekday: 'long',
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                    })}. Please reset your password before this time.
                </div>

                <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
                <p style="word-break: break-all; background: #f3f4f6; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 14px;">${resetUrl}</p>

                <div class="help-section">
                    <h3 style="margin-top: 0; color: #0369a1;">Need Help?</h3>
                    <p style="margin-bottom: 0;">If you're having trouble resetting your password or have security concerns, please contact our support team at <a href="mailto:<EMAIL>" style="color: #2563eb;"><EMAIL></a></p>
                </div>
            </div>
            <div class="footer">
                <p><strong>KAYA Finance</strong> - Modern Financial Management for SMEs</p>
                <p>This is an automated security email. Please do not reply to this message.</p>
                <p>© ${new Date().getFullYear()} KAYA Finance. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>
  `

  const text = `
Reset Your KAYA Finance Password

Hello,

We received a request to reset the password for your KAYA Finance account (${userEmail}).

SECURITY NOTICE: If you didn't request this password reset, please ignore this email. Your account remains secure.

To reset your password, visit this link:
${resetUrl}

IMPORTANT: This password reset link expires on ${new Date(expiresAt).toLocaleDateString()}.

If you're having trouble or have security concerns, please contact our support <NAME_EMAIL>

---
KAYA Finance - Modern Financial Management for SMEs
This is an automated security email. Please do not reply to this message.
© ${new Date().getFullYear()} KAYA Finance. All rights reserved.
  `

  return { subject, html, text }
}

function generateWelcomeEmail(data: Record<string, unknown>, orgName?: string): { subject: string; html: string; text: string } {
  const userName = data.user_name as string
  const organizationName = orgName || data.organization_name as string || 'KAYA Finance'
  const loginUrl = data.login_url as string
  const userRole = data.role as string || 'team member'

  const subject = `Welcome to ${organizationName}! 🎉`

  const html = `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Welcome to ${organizationName} - KAYA Finance</title>
        <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f8fafc; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; }
            .header { background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; padding: 40px 30px; text-align: center; }
            .logo { font-size: 28px; font-weight: bold; margin-bottom: 10px; }
            .content { padding: 40px 30px; }
            .welcome-card { background: #f0fdf4; border: 1px solid #bbf7d0; border-radius: 12px; padding: 30px; margin: 30px 0; text-align: center; }
            .button { display: inline-block; background: #10b981; color: white; padding: 16px 32px; text-decoration: none; border-radius: 8px; margin: 20px 0; font-weight: 600; }
            .button:hover { background: #059669; }
            .features-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 30px 0; }
            .feature-card { background: #f8fafc; border: 1px solid #e5e7eb; border-radius: 8px; padding: 20px; text-align: center; }
            .feature-icon { font-size: 24px; margin-bottom: 10px; }
            .footer { background: #f8fafc; padding: 30px; text-align: center; color: #666; font-size: 14px; border-top: 1px solid #e5e7eb; }
            .next-steps { background: #eff6ff; border: 1px solid #93c5fd; border-radius: 8px; padding: 20px; margin: 20px 0; }
            @media (max-width: 600px) { .features-grid { grid-template-columns: 1fr; } }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="logo">🏦 KAYA Finance</div>
                <p style="margin: 0; opacity: 0.9;">Modern Financial Management for SMEs</p>
            </div>
            <div class="content">
                <div class="welcome-card">
                    <h1 style="color: #065f46; margin-bottom: 15px;">🎉 Welcome to ${organizationName}!</h1>
                    <p style="font-size: 18px; color: #047857; margin: 0;">Your account has been successfully created</p>
                </div>

                <p>Hello <strong>${userName}</strong>,</p>
                <p>Congratulations! You've successfully joined <strong>${organizationName}</strong> as a <strong>${userRole}</strong> on KAYA Finance.</p>

                <p>KAYA Finance is your comprehensive financial management platform that helps businesses:</p>

                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">📊</div>
                        <h3 style="margin: 10px 0; color: #1f2937;">Track Finances</h3>
                        <p style="margin: 0; font-size: 14px; color: #6b7280;">Monitor income, expenses, and cash flow in real-time</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🧾</div>
                        <h3 style="margin: 10px 0; color: #1f2937;">Manage Invoices</h3>
                        <p style="margin: 0; font-size: 14px; color: #6b7280;">Create, send, and track professional invoices</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">💰</div>
                        <h3 style="margin: 10px 0; color: #1f2937;">Handle Payments</h3>
                        <p style="margin: 0; font-size: 14px; color: #6b7280;">Record and reconcile payments efficiently</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">📈</div>
                        <h3 style="margin: 10px 0; color: #1f2937;">Generate Reports</h3>
                        <p style="margin: 0; font-size: 14px; color: #6b7280;">Access detailed financial reports and insights</p>
                    </div>
                </div>

                <div style="text-align: center; margin: 40px 0;">
                    <a href="${loginUrl}" class="button">Start Using KAYA Finance</a>
                </div>

                <div class="next-steps">
                    <h3 style="margin-top: 0; color: #1e40af;">🚀 Next Steps</h3>
                    <ol style="margin: 0; padding-left: 20px;">
                        <li>Log in to your account using the button above</li>
                        <li>Complete your profile setup</li>
                        <li>Explore the dashboard and key features</li>
                        <li>Start managing your financial data</li>
                    </ol>
                </div>

                <p>If you have any questions or need assistance getting started, our support team is here to help at <a href="mailto:<EMAIL>" style="color: #2563eb;"><EMAIL></a></p>

                <p>Welcome aboard! 🚀</p>
                <p><strong>The KAYA Finance Team</strong></p>
            </div>
            <div class="footer">
                <p><strong>KAYA Finance</strong> - Modern Financial Management for SMEs</p>
                <p>Need help? Visit our <a href="https://kayafinance.com/help" style="color: #2563eb;">Help Center</a> or contact <a href="mailto:<EMAIL>" style="color: #2563eb;"><EMAIL></a></p>
                <p>© ${new Date().getFullYear()} KAYA Finance. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>
  `

  const text = `
Welcome to ${organizationName}! 🎉

Hello ${userName},

Congratulations! You've successfully joined ${organizationName} as a ${userRole} on KAYA Finance.

KAYA Finance is your comprehensive financial management platform that helps businesses:
• Track finances and monitor cash flow in real-time
• Manage invoices and billing efficiently
• Handle payments and reconciliation
• Generate detailed financial reports and insights

Get started: ${loginUrl}

Next Steps:
1. Log in to your account
2. Complete your profile setup
3. Explore the dashboard and key features
4. Start managing your financial data

If you have any questions or need assistance, our support team is here to <NAME_EMAIL>

Welcome aboard! 🚀
The KAYA Finance Team

---
KAYA Finance - Modern Financial Management for SMEs
Need help? Visit our Help Center <NAME_EMAIL>
© ${new Date().getFullYear()} KAYA Finance. All rights reserved.
  `

  return { subject, html, text }
}

function generateNotificationEmail(data: Record<string, unknown>): { subject: string; html: string; text: string } {
  const notificationTitle = data.title as string || 'KAYA Finance Notification'
  const notificationMessage = data.message as string || 'You have a new notification'
  const actionUrl = data.action_url as string
  const actionText = data.action_text as string || 'View Details'
  const priority = data.priority as string || 'normal'
  const category = data.category as string || 'general'
  const timestamp = data.timestamp as string || new Date().toISOString()

  const subject = notificationTitle

  const priorityColors = {
    low: '#10b981',
    normal: '#2563eb',
    high: '#f59e0b',
    urgent: '#ef4444'
  }

  const priorityColor = priorityColors[priority as keyof typeof priorityColors] || priorityColors.normal

  const html = `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${notificationTitle} - KAYA Finance</title>
        <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f8fafc; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; }
            .header { background: linear-gradient(135deg, ${priorityColor} 0%, ${priorityColor}dd 100%); color: white; padding: 30px; text-align: center; }
            .logo { font-size: 24px; font-weight: bold; margin-bottom: 8px; }
            .content { padding: 30px; }
            .notification-card { border: 2px solid ${priorityColor}; border-radius: 12px; padding: 25px; margin: 25px 0; background: ${priorityColor}08; }
            .priority-badge { display: inline-block; background: ${priorityColor}; color: white; padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; margin-bottom: 15px; }
            .button { display: inline-block; background: ${priorityColor}; color: white; padding: 14px 28px; text-decoration: none; border-radius: 8px; margin: 20px 0; font-weight: 600; }
            .button:hover { opacity: 0.9; }
            .footer { background: #f8fafc; padding: 25px; text-align: center; color: #666; font-size: 14px; border-top: 1px solid #e5e7eb; }
            .metadata { background: #f3f4f6; border-radius: 8px; padding: 15px; margin: 20px 0; font-size: 14px; color: #6b7280; }
            .category-tag { display: inline-block; background: #e5e7eb; color: #374151; padding: 2px 8px; border-radius: 12px; font-size: 12px; margin-right: 8px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="logo">🏦 KAYA Finance</div>
                <p style="margin: 0; opacity: 0.9;">Notification Alert</p>
            </div>
            <div class="content">
                <div class="notification-card">
                    <div class="priority-badge">${priority} Priority</div>
                    <h1 style="color: #1f2937; margin: 0 0 15px 0; font-size: 24px;">${notificationTitle}</h1>
                    <p style="font-size: 16px; margin: 15px 0; color: #374151;">${notificationMessage}</p>

                    ${actionUrl ? `
                    <div style="text-align: center; margin: 25px 0;">
                        <a href="${actionUrl}" class="button">${actionText}</a>
                    </div>
                    ` : ''}
                </div>

                <div class="metadata">
                    <div style="margin-bottom: 8px;">
                        <span class="category-tag">${category}</span>
                        <span style="color: #9ca3af;">•</span>
                        <span style="margin-left: 8px;">${new Date(timestamp).toLocaleDateString('en-US', {
                            weekday: 'long',
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                        })}</span>
                    </div>
                </div>

                <p style="color: #6b7280; font-size: 14px;">This notification was generated automatically by your KAYA Finance system. If you have questions about this notification, please contact your system administrator.</p>
            </div>
            <div class="footer">
                <p><strong>KAYA Finance</strong> - Modern Financial Management for SMEs</p>
                <p>Manage your notification preferences in your <a href="${actionUrl ? actionUrl.split('/').slice(0, 3).join('/') : 'https://app.kayafinance.com'}/settings" style="color: ${priorityColor};">account settings</a></p>
                <p>© ${new Date().getFullYear()} KAYA Finance. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>
  `

  const text = `
${notificationTitle}

${notificationMessage}

Priority: ${priority.toUpperCase()}
Category: ${category}
Time: ${new Date(timestamp).toLocaleDateString()}

${actionUrl ? `Take action: ${actionUrl}` : ''}

This notification was generated automatically by your KAYA Finance system.

---
KAYA Finance - Modern Financial Management for SMEs
© ${new Date().getFullYear()} KAYA Finance. All rights reserved.
  `

  return { subject, html, text }
}

function generateTestEmail(data: Record<string, unknown>): { subject: string; html: string; text: string } {
  const testMessage = data.test_message as string || 'Email configuration test'
  const timestamp = new Date().toISOString()

  const subject = 'KAYA Finance Email Configuration Test'

  const html = `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Email Test - KAYA Finance</title>
        <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f8fafc; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; }
            .header { background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; padding: 30px; text-align: center; }
            .logo { font-size: 24px; font-weight: bold; margin-bottom: 8px; }
            .content { padding: 30px; }
            .test-card { background: #f0fdf4; border: 2px solid #10b981; border-radius: 12px; padding: 25px; margin: 25px 0; text-align: center; }
            .success-icon { font-size: 48px; margin-bottom: 15px; }
            .footer { background: #f8fafc; padding: 25px; text-align: center; color: #666; font-size: 14px; border-top: 1px solid #e5e7eb; }
            .info-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 20px 0; }
            .info-item { background: #f8fafc; border: 1px solid #e5e7eb; border-radius: 8px; padding: 15px; }
            .info-label { font-weight: 600; color: #374151; font-size: 14px; }
            .info-value { color: #6b7280; font-size: 14px; margin-top: 5px; }
            @media (max-width: 600px) { .info-grid { grid-template-columns: 1fr; } }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="logo">🏦 KAYA Finance</div>
                <p style="margin: 0; opacity: 0.9;">Email Configuration Test</p>
            </div>
            <div class="content">
                <div class="test-card">
                    <div class="success-icon">✅</div>
                    <h1 style="color: #065f46; margin: 0 0 15px 0;">Email Test Successful!</h1>
                    <p style="color: #047857; margin: 0; font-size: 16px;">Your email configuration is working correctly</p>
                </div>

                <p>Congratulations! This test email confirms that your KAYA Finance email system is properly configured and operational.</p>

                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">Test Message</div>
                        <div class="info-value">${testMessage}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Test Time</div>
                        <div class="info-value">${new Date(timestamp).toLocaleDateString('en-US', {
                            weekday: 'long',
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                        })}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Email Service</div>
                        <div class="info-value">KAYA Finance Email System</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Status</div>
                        <div class="info-value">✅ Operational</div>
                    </div>
                </div>

                <p><strong>What this means:</strong></p>
                <ul>
                    <li>Email delivery is working correctly</li>
                    <li>Templates are rendering properly</li>
                    <li>SMTP/API configuration is valid</li>
                    <li>Your users will receive email notifications</li>
                </ul>

                <p>If you have any questions about your email configuration, please contact our support team at <a href="mailto:<EMAIL>" style="color: #10b981;"><EMAIL></a></p>
            </div>
            <div class="footer">
                <p><strong>KAYA Finance</strong> - Modern Financial Management for SMEs</p>
                <p>This is an automated test email from your KAYA Finance system.</p>
                <p>© ${new Date().getFullYear()} KAYA Finance. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>
  `

  const text = `
KAYA Finance Email Configuration Test

✅ Email Test Successful!

Your email configuration is working correctly.

Test Details:
- Message: ${testMessage}
- Time: ${new Date(timestamp).toLocaleDateString()}
- Service: KAYA Finance Email System
- Status: ✅ Operational

What this means:
• Email delivery is working correctly
• Templates are rendering properly
• SMTP/API configuration is valid
• Your users will receive email notifications

If you have any questions about your email configuration, please contact our support <NAME_EMAIL>

---
KAYA Finance - Modern Financial Management for SMEs
This is an automated test email from your KAYA Finance system.
© ${new Date().getFullYear()} KAYA Finance. All rights reserved.
  `

  return { subject, html, text }
}

/**
 * Get configured email provider
 */
function getEmailProvider(): EmailProvider | null {
  const provider = Deno.env.get('EMAIL_PROVIDER') || 'resend'

  switch (provider) {
    case 'resend':
      return createResendProvider()
    case 'sendgrid':
      return createSendGridProvider()
    case 'aws-ses':
      return createAWSSESProvider()
    default:
      console.error(`Unsupported email provider: ${provider}`)
      return null
  }
}

/**
 * Create Resend email provider
 */
function createResendProvider(): EmailProvider {
  const apiKey = Deno.env.get('RESEND_API_KEY')
  if (!apiKey) {
    throw new Error('RESEND_API_KEY environment variable is required')
  }

  const resend = new Resend(apiKey)

  return {
    name: 'resend',
    send: async (emailData: EmailSendData) => {
      try {
        const { data, error } = await resend.emails.send({
          from: emailData.from,
          to: emailData.to,
          subject: emailData.subject,
          html: emailData.html,
          text: emailData.text,
          reply_to: emailData.replyTo,
        })

        if (error) {
          return { error: error.message }
        }

        return { id: data?.id }
      } catch (error) {
        return { error: error.message }
      }
    }
  }
}

/**
 * Create SendGrid email provider
 */
function createSendGridProvider(): EmailProvider {
  const apiKey = Deno.env.get('SENDGRID_API_KEY')
  if (!apiKey) {
    throw new Error('SENDGRID_API_KEY environment variable is required')
  }

  return {
    name: 'sendgrid',
    send: async (emailData: EmailSendData) => {
      try {
        const response = await fetch('https://api.sendgrid.com/v3/mail/send', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            personalizations: [{
              to: emailData.to.map(email => ({ email })),
              subject: emailData.subject,
            }],
            from: { email: emailData.from.split('<')[1]?.replace('>', '') || emailData.from },
            content: [
              { type: 'text/plain', value: emailData.text },
              { type: 'text/html', value: emailData.html },
            ],
            reply_to: emailData.replyTo ? { email: emailData.replyTo } : undefined,
          }),
        })

        if (!response.ok) {
          const error = await response.text()
          return { error: `SendGrid error: ${error}` }
        }

        const messageId = response.headers.get('x-message-id')
        return { id: messageId || 'sendgrid-' + Date.now() }
      } catch (error) {
        return { error: error.message }
      }
    }
  }
}

/**
 * Create AWS SES email provider
 */
function createAWSSESProvider(): EmailProvider {
  const region = Deno.env.get('AWS_SES_REGION') || 'us-east-1'
  const accessKeyId = Deno.env.get('AWS_SES_ACCESS_KEY_ID')
  const secretAccessKey = Deno.env.get('AWS_SES_SECRET_ACCESS_KEY')

  if (!accessKeyId || !secretAccessKey) {
    throw new Error('AWS_SES_ACCESS_KEY_ID and AWS_SES_SECRET_ACCESS_KEY environment variables are required')
  }

  return {
    name: 'aws-ses',
    send: async (emailData: EmailSendData) => {
      try {
        // AWS SES implementation would go here
        // For now, return a placeholder
        return { error: 'AWS SES provider not yet implemented' }
      } catch (error) {
        return { error: error.message }
      }
    }
  }
}
