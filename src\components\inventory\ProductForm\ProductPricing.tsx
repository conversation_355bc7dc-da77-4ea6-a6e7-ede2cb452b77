import React, { useState } from 'react'
import { UseFormReturn } from 'react-hook-form'
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { But<PERSON> } from '@/components/ui/button'
import {
  AlertCircle,
  Calculator,
  Percent
} from 'lucide-react'
import { ProductFormValues } from './types'

interface ProductPricingProps {
  form: UseFormReturn<ProductFormValues>
  costPrice: number
  sellingPrice: number
  marginPercentage: string
  showPricingHelp: boolean
  onPricingHelpToggle: () => void
  onPricingChange: () => void
}

export function ProductPricing({
  form,
  costPrice,
  sellingPrice,
  marginPercentage,
  showPricingHelp,
  onPricingHelpToggle,
  onPricingChange,
}: ProductPricingProps) {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">Pricing</h3>
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={onPricingHelpToggle}
        >
          <Calculator className="h-4 w-4 mr-2" />
          Pricing Help
        </Button>
      </div>

      <div className="grid grid-cols-2 gap-4">
        {/* Cost Price */}
        <FormField
          control={form.control}
          name="cost_price"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Cost Price (UGX)</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  step="0.01"
                  min="0"
                  placeholder="0.00"
                  {...field}
                  onChange={(e) => {
                    field.onChange(parseFloat(e.target.value) || 0)
                    setTimeout(onPricingChange, 100)
                  }}
                />
              </FormControl>
              <FormDescription>
                What you pay for the product
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Selling Price */}
        <FormField
          control={form.control}
          name="selling_price"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                Selling Price (UGX)
                {costPrice > 0 && sellingPrice > 0 && (
                  <span className="ml-2 text-sm font-normal text-muted-foreground">
                    ({marginPercentage}% margin)
                  </span>
                )}
              </FormLabel>
              <FormControl>
                <Input
                  type="number"
                  step="0.01"
                  min="0"
                  placeholder="0.00"
                  {...field}
                  onChange={(e) => {
                    field.onChange(parseFloat(e.target.value) || 0)
                    setTimeout(onPricingChange, 100)
                  }}
                />
              </FormControl>
              <FormDescription>
                What you charge customers
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* Pricing Warnings */}
      {costPrice > 0 && sellingPrice > 0 && (
        <div className="space-y-2">
          {sellingPrice < costPrice && (
            <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <span className="text-sm text-red-700">
                Warning: Selling price is below cost price. You'll lose money on each sale.
              </span>
            </div>
          )}

          {parseFloat(marginPercentage) < 20 && sellingPrice >= costPrice && (
            <div className="flex items-center gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <AlertCircle className="h-4 w-4 text-yellow-600" />
              <span className="text-sm text-yellow-700">
                Low margin: Consider increasing the selling price for better profitability.
              </span>
            </div>
          )}

          {showPricingHelp && (
            <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="text-sm text-blue-700 space-y-1">
                <div className="font-medium">Pricing Calculations:</div>
                <div>• Margin: UGX {(sellingPrice - costPrice).toLocaleString()} ({marginPercentage}%)</div>
                <div>• Markup: {costPrice > 0 ? ((sellingPrice - costPrice) / costPrice * 100).toFixed(1) : '0'}%</div>
                <div className="text-xs mt-2 text-blue-600">
                  Margin = (Selling Price - Cost Price) / Selling Price × 100
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Quick Pricing Actions */}
      {costPrice > 0 && (
        <div className="flex gap-2">
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={() => {
              const newPrice = costPrice * 1.25 // 25% markup
              form.setValue('selling_price', newPrice)
              onPricingChange()
            }}
          >
            <Percent className="h-3 w-3 mr-1" />
            +25%
          </Button>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={() => {
              const newPrice = costPrice * 1.5 // 50% markup
              form.setValue('selling_price', newPrice)
              onPricingChange()
            }}
          >
            <Percent className="h-3 w-3 mr-1" />
            +50%
          </Button>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={() => {
              const newPrice = costPrice * 2 // 100% markup
              form.setValue('selling_price', newPrice)
              onPricingChange()
            }}
          >
            <Percent className="h-3 w-3 mr-1" />
            +100%
          </Button>
        </div>
      )}
    </div>
  )
}
