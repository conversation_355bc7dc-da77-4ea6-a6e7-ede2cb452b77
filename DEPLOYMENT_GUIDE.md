# Kaya Finance - Production Deployment Guide

This guide provides step-by-step instructions for deploying Kaya Finance to production.

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ installed
- npm or yarn package manager
- Git repository access
- Production Supabase project
- Domain name and hosting platform account

### 1. Environment Setup

```bash
# Clone the repository
git clone https://github.com/your-username/kaya-finance.git
cd kaya-finance

# Install dependencies
npm install

# Copy environment template
cp .env.example .env.production

# Edit production environment variables
nano .env.production
```

### 2. Configure Environment Variables

Update `.env.production` with your production values:

```env
# Application
VITE_APP_URL="https://app.kayafinance.com"
VITE_NODE_ENV="production"

# Supabase
VITE_SUPABASE_URL="https://your-project.supabase.co"
VITE_SUPABASE_ANON_KEY="your-anon-key"

# Authentication
VITE_AUTH_REDIRECT_URL="https://app.kayafinance.com/auth/callback"
VITE_AUTH_SITE_URL="https://app.kayafinance.com"

# Email
VITE_RESEND_API_KEY="re_your-api-key"
VITE_FROM_EMAIL="<EMAIL>"

# Monitoring
VITE_SENTRY_DSN="https://<EMAIL>/project-id"
```

### 3. Build and Deploy

```bash
# Run production build
npm run production:build

# Deploy (choose your platform)
npm run production:deploy netlify
# or
npm run production:deploy vercel
# or
docker-compose -f docker-compose.prod.yml up -d
```

### 4. Verify Deployment

```bash
# Run health check
npm run production:health-check

# Check application status
curl -f https://app.kayafinance.com/health
```

## 📋 Detailed Deployment Options

### Option A: Netlify Deployment

1. **Install Netlify CLI**
   ```bash
   npm install -g netlify-cli
   ```

2. **Login to Netlify**
   ```bash
   netlify login
   ```

3. **Initialize Site**
   ```bash
   netlify init
   ```

4. **Configure Build Settings**
   - Build command: `npm run build:production`
   - Publish directory: `dist`
   - Environment variables: Set in Netlify dashboard

5. **Deploy**
   ```bash
   netlify deploy --prod
   ```

6. **Configure Custom Domain**
   - Add domain in Netlify dashboard
   - Update DNS records
   - Enable HTTPS

### Option B: Vercel Deployment

1. **Install Vercel CLI**
   ```bash
   npm install -g vercel
   ```

2. **Login to Vercel**
   ```bash
   vercel login
   ```

3. **Deploy**
   ```bash
   vercel --prod
   ```

4. **Configure Environment Variables**
   ```bash
   vercel env add VITE_SUPABASE_URL
   vercel env add VITE_SUPABASE_ANON_KEY
   # Add all other environment variables
   ```

5. **Configure Custom Domain**
   ```bash
   vercel domains add app.kayafinance.com
   ```

### Option C: Docker Deployment

1. **Build Docker Image**
   ```bash
   docker build -t kaya-finance:latest .
   ```

2. **Run Container**
   ```bash
   docker run -d \
     --name kaya-finance \
     -p 80:80 \
     -p 443:443 \
     --env-file .env.production \
     kaya-finance:latest
   ```

3. **Or Use Docker Compose**
   ```bash
   docker-compose -f docker-compose.prod.yml up -d
   ```

4. **Configure Reverse Proxy**
   - Set up Nginx or Traefik
   - Configure SSL certificates
   - Set up load balancing (if needed)

## 🔧 Advanced Configuration

### SSL/HTTPS Setup

#### Let's Encrypt with Certbot
```bash
# Install certbot
sudo apt-get install certbot python3-certbot-nginx

# Obtain certificate
sudo certbot --nginx -d app.kayafinance.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

#### Custom SSL Certificate
```bash
# Copy certificates
cp your-cert.pem ssl/cert.pem
cp your-key.pem ssl/key.pem

# Update nginx configuration
# ssl_certificate /etc/nginx/ssl/cert.pem;
# ssl_certificate_key /etc/nginx/ssl/key.pem;
```

### Database Migration

```bash
# Connect to Supabase
npx supabase login

# Link to production project
npx supabase link --project-ref your-project-ref

# Run migrations
npx supabase db push

# Verify migration
npx supabase db diff
```

### Monitoring Setup

#### Sentry Error Tracking
1. Create Sentry project
2. Add DSN to environment variables
3. Configure error boundaries in React

#### Uptime Monitoring
```bash
# Using UptimeRobot API
curl -X POST \
  https://api.uptimerobot.com/v2/newMonitor \
  -H 'Content-Type: application/x-www-form-urlencoded' \
  -d 'api_key=your-api-key&format=json&type=1&url=https://app.kayafinance.com&friendly_name=Kaya Finance'
```

### Performance Optimization

#### CDN Setup
1. Configure CloudFlare or AWS CloudFront
2. Set up asset caching rules
3. Enable compression and minification

#### Database Optimization
```sql
-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_invoices_org_id ON invoices(org_id);
CREATE INDEX IF NOT EXISTS idx_customers_org_id ON customers(org_id);
CREATE INDEX IF NOT EXISTS idx_products_org_id ON products(org_id);
```

## 🔒 Security Hardening

### Firewall Configuration
```bash
# UFW (Ubuntu)
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable

# Fail2ban
sudo apt-get install fail2ban
sudo systemctl enable fail2ban
```

### Security Headers
Ensure these headers are set in your web server:
- `Strict-Transport-Security`
- `X-Frame-Options`
- `X-Content-Type-Options`
- `X-XSS-Protection`
- `Content-Security-Policy`

### Regular Security Updates
```bash
# Ubuntu/Debian
sudo apt-get update && sudo apt-get upgrade

# Docker images
docker pull node:18-alpine
docker pull nginx:alpine
```

## 📊 Monitoring and Maintenance

### Health Checks
```bash
# Application health
curl -f https://app.kayafinance.com/health

# Database health
curl -f https://your-project.supabase.co/rest/v1/

# SSL certificate expiry
echo | openssl s_client -servername app.kayafinance.com -connect app.kayafinance.com:443 2>/dev/null | openssl x509 -noout -dates
```

### Log Monitoring
```bash
# Application logs
docker logs kaya-finance

# Nginx logs
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log

# System logs
journalctl -u nginx -f
```

### Backup Procedures
```bash
# Database backup (Supabase)
npx supabase db dump > backup-$(date +%Y%m%d).sql

# File backup
tar -czf backup-files-$(date +%Y%m%d).tar.gz /path/to/files
```

## 🆘 Troubleshooting

### Common Issues

#### Build Failures
```bash
# Clear cache and rebuild
npm run clean
npm install
npm run build:production
```

#### SSL Certificate Issues
```bash
# Check certificate
openssl x509 -in cert.pem -text -noout

# Renew Let's Encrypt
sudo certbot renew --dry-run
```

#### Database Connection Issues
```bash
# Test connection
curl -H "apikey: your-anon-key" https://your-project.supabase.co/rest/v1/
```

### Rollback Procedure
```bash
# Docker rollback
docker stop kaya-finance
docker run -d --name kaya-finance-old previous-image:tag

# Git rollback
git revert HEAD
npm run production:build
npm run production:deploy
```

## 📞 Support

- **Documentation**: [GitHub Wiki](https://github.com/your-username/kaya-finance/wiki)
- **Issues**: [GitHub Issues](https://github.com/your-username/kaya-finance/issues)
- **Email**: <EMAIL>

---

**Note**: Always test deployments in a staging environment before deploying to production.
