-- =====================================================
-- EMAIL RATE LIMITING MIGRATION
-- =====================================================
-- Migration: 20250722_email_rate_limiting.sql
-- Description: Email rate limiting and monitoring tables
-- Author: Kaya Finance Team
-- Date: 2025-07-22

-- =====================================================
-- STEP 1: CREATE RATE LIMITING TABLES
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🚦 Creating email rate limiting tables...';
END $$;

-- Email rate limit violations table
CREATE TABLE IF NOT EXISTS email_rate_limit_violations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    identifier TEXT NOT NULL,
    limit_type TEXT NOT NULL CHECK (limit_type IN ('minute', 'hour', 'day', 'burst')),
    limit_value INTEGER NOT NULL,
    current_count INTEGER NOT NULL,
    org_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    email_type TEXT,
    user_agent TEXT,
    ip_address INET,
    violated_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Email rate limit configurations table (per organization)
CREATE TABLE IF NOT EXISTS email_rate_limit_configs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    per_minute INTEGER DEFAULT 10,
    per_hour INTEGER DEFAULT 100,
    per_day INTEGER DEFAULT 1000,
    burst_limit INTEGER DEFAULT 5,
    enabled BOOLEAN DEFAULT true,
    alert_threshold_percentage INTEGER DEFAULT 80,
    alert_email TEXT,
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(org_id)
);

-- Email sending statistics table (for monitoring)
CREATE TABLE IF NOT EXISTS email_sending_stats (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    hour INTEGER CHECK (hour >= 0 AND hour <= 23),
    emails_sent INTEGER DEFAULT 0,
    emails_failed INTEGER DEFAULT 0,
    emails_bounced INTEGER DEFAULT 0,
    rate_limited INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(org_id, date, hour)
);

-- Email rate limit alerts table
CREATE TABLE IF NOT EXISTS email_rate_limit_alerts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    alert_type TEXT NOT NULL CHECK (alert_type IN ('threshold_exceeded', 'limit_reached', 'suspicious_activity')),
    threshold_percentage INTEGER,
    current_usage INTEGER,
    limit_value INTEGER,
    time_window TEXT,
    message TEXT,
    resolved BOOLEAN DEFAULT false,
    resolved_at TIMESTAMP WITH TIME ZONE,
    resolved_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- STEP 2: CREATE INDEXES FOR PERFORMANCE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '📊 Creating rate limiting indexes...';
END $$;

-- Rate limit violations indexes
CREATE INDEX IF NOT EXISTS idx_email_rate_limit_violations_identifier ON email_rate_limit_violations(identifier);
CREATE INDEX IF NOT EXISTS idx_email_rate_limit_violations_org_id ON email_rate_limit_violations(org_id);
CREATE INDEX IF NOT EXISTS idx_email_rate_limit_violations_violated_at ON email_rate_limit_violations(violated_at DESC);
CREATE INDEX IF NOT EXISTS idx_email_rate_limit_violations_limit_type ON email_rate_limit_violations(limit_type);

-- Rate limit configs indexes
CREATE INDEX IF NOT EXISTS idx_email_rate_limit_configs_org_id ON email_rate_limit_configs(org_id);
CREATE INDEX IF NOT EXISTS idx_email_rate_limit_configs_enabled ON email_rate_limit_configs(enabled) WHERE enabled = true;

-- Email sending stats indexes
CREATE INDEX IF NOT EXISTS idx_email_sending_stats_org_id_date ON email_sending_stats(org_id, date DESC);
CREATE INDEX IF NOT EXISTS idx_email_sending_stats_date_hour ON email_sending_stats(date DESC, hour DESC);

-- Rate limit alerts indexes
CREATE INDEX IF NOT EXISTS idx_email_rate_limit_alerts_org_id ON email_rate_limit_alerts(org_id);
CREATE INDEX IF NOT EXISTS idx_email_rate_limit_alerts_resolved ON email_rate_limit_alerts(resolved) WHERE resolved = false;
CREATE INDEX IF NOT EXISTS idx_email_rate_limit_alerts_created_at ON email_rate_limit_alerts(created_at DESC);

-- =====================================================
-- STEP 3: CREATE RATE LIMITING FUNCTIONS
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '⚙️ Creating rate limiting functions...';
END $$;

-- Function to get rate limit configuration for organization
CREATE OR REPLACE FUNCTION get_rate_limit_config(org_id_param UUID)
RETURNS JSONB AS $$
DECLARE
    config_record email_rate_limit_configs%ROWTYPE;
    default_config JSONB;
BEGIN
    -- Try to get organization-specific config
    SELECT * INTO config_record
    FROM email_rate_limit_configs
    WHERE org_id = org_id_param AND enabled = true;
    
    IF FOUND THEN
        RETURN jsonb_build_object(
            'per_minute', config_record.per_minute,
            'per_hour', config_record.per_hour,
            'per_day', config_record.per_day,
            'burst_limit', config_record.burst_limit,
            'alert_threshold_percentage', config_record.alert_threshold_percentage,
            'alert_email', config_record.alert_email
        );
    ELSE
        -- Return default configuration
        RETURN jsonb_build_object(
            'per_minute', 10,
            'per_hour', 100,
            'per_day', 1000,
            'burst_limit', 5,
            'alert_threshold_percentage', 80,
            'alert_email', null
        );
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to record email sending statistics
CREATE OR REPLACE FUNCTION record_email_stat(
    org_id_param UUID,
    stat_type TEXT, -- 'sent', 'failed', 'bounced', 'rate_limited'
    increment_by INTEGER DEFAULT 1
)
RETURNS VOID AS $$
DECLARE
    current_date DATE := CURRENT_DATE;
    current_hour INTEGER := EXTRACT(hour FROM NOW());
BEGIN
    INSERT INTO email_sending_stats (org_id, date, hour, emails_sent, emails_failed, emails_bounced, rate_limited)
    VALUES (
        org_id_param,
        current_date,
        current_hour,
        CASE WHEN stat_type = 'sent' THEN increment_by ELSE 0 END,
        CASE WHEN stat_type = 'failed' THEN increment_by ELSE 0 END,
        CASE WHEN stat_type = 'bounced' THEN increment_by ELSE 0 END,
        CASE WHEN stat_type = 'rate_limited' THEN increment_by ELSE 0 END
    )
    ON CONFLICT (org_id, date, hour) DO UPDATE SET
        emails_sent = email_sending_stats.emails_sent + CASE WHEN stat_type = 'sent' THEN increment_by ELSE 0 END,
        emails_failed = email_sending_stats.emails_failed + CASE WHEN stat_type = 'failed' THEN increment_by ELSE 0 END,
        emails_bounced = email_sending_stats.emails_bounced + CASE WHEN stat_type = 'bounced' THEN increment_by ELSE 0 END,
        rate_limited = email_sending_stats.rate_limited + CASE WHEN stat_type = 'rate_limited' THEN increment_by ELSE 0 END,
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if rate limit alert should be triggered
CREATE OR REPLACE FUNCTION check_rate_limit_alert(
    org_id_param UUID,
    current_usage INTEGER,
    limit_value INTEGER,
    time_window TEXT
)
RETURNS BOOLEAN AS $$
DECLARE
    config_record email_rate_limit_configs%ROWTYPE;
    threshold_percentage INTEGER;
    usage_percentage INTEGER;
    existing_alert_count INTEGER;
BEGIN
    -- Get organization config
    SELECT * INTO config_record
    FROM email_rate_limit_configs
    WHERE org_id = org_id_param;
    
    threshold_percentage := COALESCE(config_record.alert_threshold_percentage, 80);
    usage_percentage := (current_usage * 100) / NULLIF(limit_value, 0);
    
    -- Check if threshold is exceeded
    IF usage_percentage >= threshold_percentage THEN
        -- Check if we already have an unresolved alert for this time window
        SELECT COUNT(*) INTO existing_alert_count
        FROM email_rate_limit_alerts
        WHERE org_id = org_id_param
        AND time_window = check_rate_limit_alert.time_window
        AND resolved = false
        AND created_at > NOW() - INTERVAL '1 hour'; -- Don't spam alerts
        
        IF existing_alert_count = 0 THEN
            -- Create new alert
            INSERT INTO email_rate_limit_alerts (
                org_id,
                alert_type,
                threshold_percentage,
                current_usage,
                limit_value,
                time_window,
                message
            )
            VALUES (
                org_id_param,
                CASE WHEN usage_percentage >= 100 THEN 'limit_reached' ELSE 'threshold_exceeded' END,
                threshold_percentage,
                current_usage,
                limit_value,
                check_rate_limit_alert.time_window,
                format('Email rate limit %s%% reached for %s window (%s/%s emails)',
                       usage_percentage, check_rate_limit_alert.time_window, current_usage, limit_value)
            );
            
            RETURN true;
        END IF;
    END IF;
    
    RETURN false;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get email sending analytics
CREATE OR REPLACE FUNCTION get_email_analytics(
    org_id_param UUID,
    start_date DATE DEFAULT CURRENT_DATE - INTERVAL '7 days',
    end_date DATE DEFAULT CURRENT_DATE
)
RETURNS JSONB AS $$
DECLARE
    analytics JSONB;
BEGIN
    SELECT jsonb_build_object(
        'total_sent', COALESCE(SUM(emails_sent), 0),
        'total_failed', COALESCE(SUM(emails_failed), 0),
        'total_bounced', COALESCE(SUM(emails_bounced), 0),
        'total_rate_limited', COALESCE(SUM(rate_limited), 0),
        'success_rate', ROUND(
            (COALESCE(SUM(emails_sent), 0)::DECIMAL / 
             NULLIF(COALESCE(SUM(emails_sent), 0) + COALESCE(SUM(emails_failed), 0), 0)) * 100, 2
        ),
        'daily_breakdown', daily_stats,
        'hourly_pattern', hourly_stats
    ) INTO analytics
    FROM (
        SELECT 
            emails_sent,
            emails_failed,
            emails_bounced,
            rate_limited,
            jsonb_object_agg(DISTINCT date, daily_totals) as daily_stats,
            jsonb_object_agg(DISTINCT hour, hourly_totals) as hourly_stats
        FROM (
            SELECT 
                emails_sent,
                emails_failed,
                emails_bounced,
                rate_limited,
                date,
                hour,
                SUM(emails_sent) OVER (PARTITION BY date) as daily_totals,
                SUM(emails_sent) OVER (PARTITION BY hour) as hourly_totals
            FROM email_sending_stats
            WHERE org_id = org_id_param
            AND date BETWEEN start_date AND end_date
        ) subq
        GROUP BY emails_sent, emails_failed, emails_bounced, rate_limited
    ) analytics_data;
    
    RETURN COALESCE(analytics, '{}'::jsonb);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to clean up old rate limit data
CREATE OR REPLACE FUNCTION cleanup_rate_limit_data(days_old INTEGER DEFAULT 30)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER := 0;
    temp_count INTEGER;
BEGIN
    -- Clean up old violations
    DELETE FROM email_rate_limit_violations
    WHERE violated_at < NOW() - INTERVAL '1 day' * days_old;
    GET DIAGNOSTICS temp_count = ROW_COUNT;
    deleted_count := deleted_count + temp_count;
    
    -- Clean up old sending stats (keep longer for analytics)
    DELETE FROM email_sending_stats
    WHERE date < CURRENT_DATE - INTERVAL '1 day' * (days_old * 3);
    GET DIAGNOSTICS temp_count = ROW_COUNT;
    deleted_count := deleted_count + temp_count;
    
    -- Clean up resolved alerts older than 7 days
    DELETE FROM email_rate_limit_alerts
    WHERE resolved = true
    AND resolved_at < NOW() - INTERVAL '7 days';
    GET DIAGNOSTICS temp_count = ROW_COUNT;
    deleted_count := deleted_count + temp_count;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- STEP 4: CREATE TRIGGERS
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔧 Creating rate limiting triggers...';
END $$;

-- Add updated_at triggers
DROP TRIGGER IF EXISTS update_email_rate_limit_configs_updated_at ON email_rate_limit_configs;
CREATE TRIGGER update_email_rate_limit_configs_updated_at
    BEFORE UPDATE ON email_rate_limit_configs
    FOR EACH ROW EXECUTE FUNCTION update_email_updated_at_column();

DROP TRIGGER IF EXISTS update_email_sending_stats_updated_at ON email_sending_stats;
CREATE TRIGGER update_email_sending_stats_updated_at
    BEFORE UPDATE ON email_sending_stats
    FOR EACH ROW EXECUTE FUNCTION update_email_updated_at_column();

-- =====================================================
-- STEP 5: CREATE RLS POLICIES
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🔒 Creating rate limiting RLS policies...';
END $$;

-- Enable RLS on rate limiting tables
ALTER TABLE email_rate_limit_violations ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_rate_limit_configs ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_sending_stats ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_rate_limit_alerts ENABLE ROW LEVEL SECURITY;

-- Rate limit violations policies
CREATE POLICY "email_rate_limit_violations_select_policy" ON email_rate_limit_violations
    FOR SELECT USING (
        auth.role() = 'service_role' OR
        org_id IN (SELECT p.org_id FROM profiles p WHERE p.id = auth.uid())
    );

CREATE POLICY "email_rate_limit_violations_insert_policy" ON email_rate_limit_violations
    FOR INSERT WITH CHECK (auth.role() = 'service_role');

-- Rate limit configs policies
CREATE POLICY "email_rate_limit_configs_select_policy" ON email_rate_limit_configs
    FOR SELECT USING (
        org_id IN (SELECT p.org_id FROM profiles p WHERE p.id = auth.uid())
    );

CREATE POLICY "email_rate_limit_configs_insert_policy" ON email_rate_limit_configs
    FOR INSERT WITH CHECK (
        org_id IN (SELECT p.org_id FROM profiles p WHERE p.id = auth.uid() AND p.role = 'admin')
    );

CREATE POLICY "email_rate_limit_configs_update_policy" ON email_rate_limit_configs
    FOR UPDATE USING (
        org_id IN (SELECT p.org_id FROM profiles p WHERE p.id = auth.uid() AND p.role = 'admin')
    );

CREATE POLICY "email_rate_limit_configs_delete_policy" ON email_rate_limit_configs
    FOR DELETE USING (
        org_id IN (SELECT p.org_id FROM profiles p WHERE p.id = auth.uid() AND p.role = 'admin')
    );

-- Email sending stats policies
CREATE POLICY "email_sending_stats_select_policy" ON email_sending_stats
    FOR SELECT USING (
        auth.role() = 'service_role' OR
        org_id IN (SELECT p.org_id FROM profiles p WHERE p.id = auth.uid())
    );

CREATE POLICY "email_sending_stats_insert_policy" ON email_sending_stats
    FOR INSERT WITH CHECK (auth.role() = 'service_role');

CREATE POLICY "email_sending_stats_update_policy" ON email_sending_stats
    FOR UPDATE USING (auth.role() = 'service_role');

-- Rate limit alerts policies
CREATE POLICY "email_rate_limit_alerts_select_policy" ON email_rate_limit_alerts
    FOR SELECT USING (
        org_id IN (SELECT p.org_id FROM profiles p WHERE p.id = auth.uid())
    );

CREATE POLICY "email_rate_limit_alerts_insert_policy" ON email_rate_limit_alerts
    FOR INSERT WITH CHECK (auth.role() = 'service_role');

CREATE POLICY "email_rate_limit_alerts_update_policy" ON email_rate_limit_alerts
    FOR UPDATE USING (
        org_id IN (SELECT p.org_id FROM profiles p WHERE p.id = auth.uid() AND p.role = 'admin')
    );

-- =====================================================
-- STEP 6: INSERT DEFAULT CONFIGURATIONS
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '⚙️ Creating default rate limit configurations...';
END $$;

-- Insert default rate limit configurations for existing organizations
INSERT INTO email_rate_limit_configs (org_id, per_minute, per_hour, per_day, burst_limit, enabled)
SELECT
    id,
    10,  -- 10 emails per minute
    100, -- 100 emails per hour
    1000, -- 1000 emails per day
    5,   -- 5 emails burst limit
    true
FROM organizations
ON CONFLICT (org_id) DO NOTHING;

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '✅ EMAIL RATE LIMITING MIGRATION COMPLETED!';
    RAISE NOTICE '====================================================';
    RAISE NOTICE '';
    RAISE NOTICE '📧 CREATED TABLES:';
    RAISE NOTICE '  • email_rate_limit_violations - Rate limit violation tracking';
    RAISE NOTICE '  • email_rate_limit_configs - Organization rate limit settings';
    RAISE NOTICE '  • email_sending_stats - Email sending statistics';
    RAISE NOTICE '  • email_rate_limit_alerts - Rate limit alerts and notifications';
    RAISE NOTICE '';
    RAISE NOTICE '⚙️ CREATED FUNCTIONS:';
    RAISE NOTICE '  • get_rate_limit_config() - Get org rate limit configuration';
    RAISE NOTICE '  • record_email_stat() - Record email sending statistics';
    RAISE NOTICE '  • check_rate_limit_alert() - Check and create rate limit alerts';
    RAISE NOTICE '  • get_email_analytics() - Get email sending analytics';
    RAISE NOTICE '  • cleanup_rate_limit_data() - Clean old rate limit data';
    RAISE NOTICE '';
    RAISE NOTICE '🔒 CREATED RLS POLICIES:';
    RAISE NOTICE '  • Organization-based access control';
    RAISE NOTICE '  • Admin-only configuration management';
    RAISE NOTICE '';
    RAISE NOTICE '⚙️ DEFAULT CONFIGURATIONS:';
    RAISE NOTICE '  • 10 emails per minute';
    RAISE NOTICE '  • 100 emails per hour';
    RAISE NOTICE '  • 1000 emails per day';
    RAISE NOTICE '  • 5 emails burst limit';
    RAISE NOTICE '';
    RAISE NOTICE '🎉 Email rate limiting is ready!';
    RAISE NOTICE '';
END $$;
