import React, { useState } from 'react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Progress } from '@/components/ui/progress'
import { Checkbox } from '@/components/ui/checkbox'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  AlertTriangle,
  Database,
  Clock,
  Shield,
  FileText,
  Users,
  Receipt,
  CreditCard,
  Calendar,
  CheckCircle,
  XCircle,
  Upload
} from 'lucide-react'
import { BackupMetadata } from '@/hooks/useBackupManagement'
import { formatBytes, formatDate } from '@/lib/utils'
import { useAuth } from '@/hooks/useAuthHook'

interface RestoreBackupDialogProps {
  backup: BackupMetadata | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onRestore: (options: RestoreOptions) => Promise<void>
  isRestoring?: boolean
  restoreProgress?: number
}

export interface RestoreOptions {
  backupId: string
  restoreType: 'full' | 'partial' | 'point_in_time'
  restoreMode: 'replace' | 'merge' | 'preview'
  selectedTables?: string[]
  restorePoint?: string
  notes?: string
}

const AVAILABLE_TABLES = [
  { id: 'customers', name: 'Customers', icon: Users, description: 'Customer information and contacts' },
  { id: 'vendors', name: 'Vendors', icon: Users, description: 'Vendor/supplier information' },
  { id: 'invoices', name: 'Invoices', icon: Receipt, description: 'Sales invoices and billing' },
  { id: 'bills', name: 'Bills', icon: FileText, description: 'Purchase bills and expenses' },
  { id: 'payments', name: 'Payments', icon: CreditCard, description: 'Payment records and transactions' },
  { id: 'accounts', name: 'Chart of Accounts', icon: Database, description: 'Account structure and balances' },
  { id: 'journal_entries', name: 'Journal Entries', icon: FileText, description: 'Accounting journal entries' },
  { id: 'organizations', name: 'Organization Settings', icon: Shield, description: 'Organization configuration' }
]

export function RestoreBackupDialog({
  backup,
  open,
  onOpenChange,
  onRestore,
  isRestoring = false,
  restoreProgress = 0
}: RestoreBackupDialogProps) {
  const { profile } = useAuth()
  const [restoreType, setRestoreType] = useState<'full' | 'partial' | 'point_in_time'>('full')
  const [restoreMode, setRestoreMode] = useState<'replace' | 'merge' | 'preview'>('preview')
  const [selectedTables, setSelectedTables] = useState<string[]>([])
  const [restorePoint, setRestorePoint] = useState('')
  const [notes, setNotes] = useState('')
  const [confirmDangerous, setConfirmDangerous] = useState(false)

  if (!backup) return null

  const isAdmin = profile?.role === 'admin'
  const isDangerousOperation = restoreMode === 'replace'
  const canProceed = restoreType === 'full' || selectedTables.length > 0

  const handleRestore = async () => {
    if (!canProceed || (isDangerousOperation && !confirmDangerous)) return

    const options: RestoreOptions = {
      backupId: backup.id,
      restoreType,
      restoreMode,
      selectedTables: restoreType === 'partial' ? selectedTables : undefined,
      restorePoint: restoreType === 'point_in_time' ? restorePoint : undefined,
      notes
    }

    await onRestore(options)
  }

  const handleTableToggle = (tableId: string, checked: boolean) => {
    if (checked) {
      setSelectedTables(prev => [...prev, tableId])
    } else {
      setSelectedTables(prev => prev.filter(id => id !== tableId))
    }
  }

  const selectAllTables = () => {
    setSelectedTables(AVAILABLE_TABLES.map(table => table.id))
  }

  const clearAllTables = () => {
    setSelectedTables([])
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-scroll">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Restore Backup
          </DialogTitle>
          <DialogDescription>
            Restore data from backup created on {formatDate(backup.created_at)}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Backup Information */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-medium mb-2">Backup Details</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">Type:</span>
                <Badge variant="outline" className="ml-2">
                  {backup.backup_type.charAt(0).toUpperCase() + backup.backup_type.slice(1)}
                </Badge>
              </div>
              <div>
                <span className="text-muted-foreground">Size:</span>
                <span className="ml-2">{formatBytes(backup.size_bytes)}</span>
              </div>
              <div>
                <span className="text-muted-foreground">Tables:</span>
                <span className="ml-2">{backup.table_count} tables</span>
              </div>
              <div>
                <span className="text-muted-foreground">Records:</span>
                <span className="ml-2">{backup.record_count.toLocaleString()} records</span>
              </div>
            </div>
          </div>

          {/* Admin Warning */}
          {!isAdmin && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Only administrators can perform data restoration. Please contact your system administrator.
              </AlertDescription>
            </Alert>
          )}

          {isAdmin && (
            <>
              {/* Restore Type Selection */}
              <div className="space-y-3">
                <Label className="text-base font-medium">Restoration Type</Label>
                <RadioGroup value={restoreType} onValueChange={(value: string) => setRestoreType(value as 'full' | 'partial' | 'point_in_time')}>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="full" id="full" />
                    <Label htmlFor="full" className="flex items-center gap-2">
                      <Database className="h-4 w-4" />
                      Full Restoration
                      <span className="text-sm text-muted-foreground">- Restore all data from backup</span>
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="partial" id="partial" />
                    <Label htmlFor="partial" className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      Partial Restoration
                      <span className="text-sm text-muted-foreground">- Restore selected tables only</span>
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="point_in_time" id="point_in_time" />
                    <Label htmlFor="point_in_time" className="flex items-center gap-2">
                      <Clock className="h-4 w-4" />
                      Point-in-Time Restoration
                      <span className="text-sm text-muted-foreground">- Restore to specific timestamp</span>
                    </Label>
                  </div>
                </RadioGroup>
              </div>

              {/* Table Selection for Partial Restore */}
              {restoreType === 'partial' && (
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label className="text-base font-medium">Select Tables to Restore</Label>
                    <div className="space-x-2">
                      <Button variant="outline" size="sm" onClick={selectAllTables}>
                        Select All
                      </Button>
                      <Button variant="outline" size="sm" onClick={clearAllTables}>
                        Clear All
                      </Button>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-3">
                    {AVAILABLE_TABLES.map((table) => (
                      <div key={table.id} className="flex items-start space-x-2 p-3 border rounded-lg">
                        <Checkbox
                          id={table.id}
                          checked={selectedTables.includes(table.id)}
                          onCheckedChange={(checked) => handleTableToggle(table.id, !!checked)}
                        />
                        <div className="flex-1">
                          <Label htmlFor={table.id} className="flex items-center gap-2 font-medium">
                            <table.icon className="h-4 w-4" />
                            {table.name}
                          </Label>
                          <p className="text-xs text-muted-foreground mt-1">{table.description}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Restore Mode Selection */}
              <div className="space-y-3">
                <Label className="text-base font-medium">Restoration Mode</Label>
                <RadioGroup value={restoreMode} onValueChange={(value: string) => setRestoreMode(value as 'preview' | 'merge' | 'replace')}>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="preview" id="preview" />
                    <Label htmlFor="preview" className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-blue-500" />
                      Preview Mode
                      <span className="text-sm text-muted-foreground">- Show what would be restored (safe)</span>
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="merge" id="merge" />
                    <Label htmlFor="merge" className="flex items-center gap-2">
                      <Database className="h-4 w-4 text-green-500" />
                      Merge Mode
                      <span className="text-sm text-muted-foreground">- Combine with existing data</span>
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="replace" id="replace" />
                    <Label htmlFor="replace" className="flex items-center gap-2">
                      <AlertTriangle className="h-4 w-4 text-red-500" />
                      Replace Mode
                      <span className="text-sm text-muted-foreground">- Overwrite existing data (dangerous)</span>
                    </Label>
                  </div>
                </RadioGroup>
              </div>

              {/* Dangerous Operation Warning */}
              {isDangerousOperation && (
                <Alert variant="destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    <div className="space-y-2">
                      <p className="font-medium">⚠️ DANGEROUS OPERATION</p>
                      <p>Replace mode will permanently overwrite your current data. This action cannot be undone.</p>
                      <div className="flex items-center space-x-2 mt-3">
                        <Checkbox
                          id="confirm-dangerous"
                          checked={confirmDangerous}
                          onCheckedChange={setConfirmDangerous}
                        />
                        <Label htmlFor="confirm-dangerous" className="text-sm">
                          I understand this will permanently overwrite existing data
                        </Label>
                      </div>
                    </div>
                  </AlertDescription>
                </Alert>
              )}

              {/* Notes */}
              <div className="space-y-2">
                <Label htmlFor="notes">Restoration Notes (Optional)</Label>
                <Textarea
                  id="notes"
                  placeholder="Add notes about why this restoration is being performed..."
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  rows={3}
                />
              </div>

              {/* Progress Display */}
              {isRestoring && (
                <Alert>
                  <Upload className="h-4 w-4" />
                  <AlertDescription>
                    <div className="space-y-2">
                      <p>Restoration in progress... Please do not close this dialog.</p>
                      <Progress value={restoreProgress} className="w-full" />
                      <p className="text-sm text-muted-foreground">{restoreProgress}% complete</p>
                    </div>
                  </AlertDescription>
                </Alert>
              )}
            </>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={isRestoring}>
            Cancel
          </Button>
          {isAdmin && (
            <Button
              onClick={handleRestore}
              disabled={!canProceed || (isDangerousOperation && !confirmDangerous) || isRestoring}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isRestoring ? 'Restoring...' : `Start ${restoreMode === 'preview' ? 'Preview' : 'Restoration'}`}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
