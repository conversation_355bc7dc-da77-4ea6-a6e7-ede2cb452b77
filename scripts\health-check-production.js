#!/usr/bin/env node

/**
 * Kaya Finance - Production Health Check Script
 * This script performs comprehensive health checks on the deployed application
 */

const https = require('https');
const http = require('http');
const { URL } = require('url');

// Configuration
const config = {
  baseUrl: process.env.HEALTH_CHECK_URL || 'https://app.kayafinance.com',
  timeout: 10000, // 10 seconds
  retries: 3,
  endpoints: [
    '/',
    '/auth/login',
    '/api/health',
  ],
  expectedHeaders: {
    'x-frame-options': 'DENY',
    'x-content-type-options': 'nosniff',
    'x-xss-protection': '1; mode=block',
  }
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// Logging functions
const log = {
  info: (msg) => console.log(`${colors.blue}ℹ${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}✅${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}❌${colors.reset} ${msg}`),
  header: (msg) => console.log(`\n${colors.cyan}${msg}${colors.reset}`),
};

// HTTP request helper
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === 'https:';
    const client = isHttps ? https : http;
    
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      timeout: config.timeout,
      headers: {
        'User-Agent': 'Kaya-Finance-Health-Check/1.0',
        ...options.headers,
      },
    };

    const req = client.request(requestOptions, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: data,
          responseTime: Date.now() - startTime,
        });
      });
    });

    const startTime = Date.now();
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    req.end();
  });
}

// Retry wrapper
async function withRetry(fn, retries = config.retries) {
  for (let i = 0; i < retries; i++) {
    try {
      return await fn();
    } catch (error) {
      if (i === retries - 1) throw error;
      log.warning(`Attempt ${i + 1} failed, retrying...`);
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
    }
  }
}

// Health check functions
async function checkEndpoint(endpoint) {
  const url = `${config.baseUrl}${endpoint}`;
  log.info(`Checking ${url}`);
  
  try {
    const response = await withRetry(() => makeRequest(url));
    
    // Check status code
    if (response.statusCode >= 200 && response.statusCode < 400) {
      log.success(`${endpoint} - HTTP ${response.statusCode} (${response.responseTime}ms)`);
      return { success: true, statusCode: response.statusCode, responseTime: response.responseTime, headers: response.headers };
    } else {
      log.error(`${endpoint} - HTTP ${response.statusCode}`);
      return { success: false, statusCode: response.statusCode, error: `HTTP ${response.statusCode}` };
    }
  } catch (error) {
    log.error(`${endpoint} - ${error.message}`);
    return { success: false, error: error.message };
  }
}

async function checkSecurityHeaders() {
  log.header('🔒 Security Headers Check');
  
  try {
    const response = await withRetry(() => makeRequest(config.baseUrl));
    const headers = response.headers;
    let allPassed = true;
    
    for (const [headerName, expectedValue] of Object.entries(config.expectedHeaders)) {
      const actualValue = headers[headerName.toLowerCase()];
      
      if (actualValue) {
        if (expectedValue && !actualValue.includes(expectedValue)) {
          log.warning(`${headerName}: Expected "${expectedValue}", got "${actualValue}"`);
          allPassed = false;
        } else {
          log.success(`${headerName}: ${actualValue}`);
        }
      } else {
        log.error(`${headerName}: Missing`);
        allPassed = false;
      }
    }
    
    // Check for additional security headers
    const additionalHeaders = [
      'strict-transport-security',
      'content-security-policy',
      'referrer-policy',
    ];
    
    additionalHeaders.forEach(header => {
      if (headers[header]) {
        log.success(`${header}: Present`);
      } else {
        log.warning(`${header}: Missing (recommended)`);
      }
    });
    
    return allPassed;
  } catch (error) {
    log.error(`Security headers check failed: ${error.message}`);
    return false;
  }
}

async function checkPerformance() {
  log.header('⚡ Performance Check');
  
  const performanceTests = [
    { name: 'Homepage Load Time', endpoint: '/', maxTime: 3000 },
    { name: 'Login Page Load Time', endpoint: '/auth/login', maxTime: 2000 },
  ];
  
  let allPassed = true;
  
  for (const test of performanceTests) {
    try {
      const response = await withRetry(() => makeRequest(`${config.baseUrl}${test.endpoint}`));
      
      if (response.responseTime <= test.maxTime) {
        log.success(`${test.name}: ${response.responseTime}ms (target: <${test.maxTime}ms)`);
      } else {
        log.warning(`${test.name}: ${response.responseTime}ms (target: <${test.maxTime}ms)`);
        allPassed = false;
      }
    } catch (error) {
      log.error(`${test.name}: Failed - ${error.message}`);
      allPassed = false;
    }
  }
  
  return allPassed;
}

async function checkSSL() {
  log.header('🔐 SSL Certificate Check');
  
  if (!config.baseUrl.startsWith('https://')) {
    log.warning('Application is not using HTTPS');
    return false;
  }
  
  try {
    const urlObj = new URL(config.baseUrl);
    
    return new Promise((resolve) => {
      const req = https.request({
        hostname: urlObj.hostname,
        port: 443,
        path: '/',
        method: 'GET',
        timeout: config.timeout,
      }, (res) => {
        const cert = res.socket.getPeerCertificate();
        
        if (cert && cert.valid_to) {
          const expiryDate = new Date(cert.valid_to);
          const now = new Date();
          const daysUntilExpiry = Math.ceil((expiryDate - now) / (1000 * 60 * 60 * 24));
          
          if (daysUntilExpiry > 30) {
            log.success(`SSL certificate valid until ${expiryDate.toDateString()} (${daysUntilExpiry} days)`);
            resolve(true);
          } else if (daysUntilExpiry > 0) {
            log.warning(`SSL certificate expires soon: ${expiryDate.toDateString()} (${daysUntilExpiry} days)`);
            resolve(false);
          } else {
            log.error('SSL certificate has expired');
            resolve(false);
          }
        } else {
          log.error('Unable to retrieve SSL certificate information');
          resolve(false);
        }
      });
      
      req.on('error', (error) => {
        log.error(`SSL check failed: ${error.message}`);
        resolve(false);
      });
      
      req.end();
    });
  } catch (error) {
    log.error(`SSL check failed: ${error.message}`);
    return false;
  }
}

// Main health check function
async function runHealthCheck() {
  console.log(`${colors.blue}🏥 Kaya Finance - Production Health Check${colors.reset}`);
  console.log(`Target: ${config.baseUrl}`);
  console.log(`Started at: ${new Date().toISOString()}\n`);
  
  const results = {
    endpoints: {},
    securityHeaders: false,
    performance: false,
    ssl: false,
    overall: false,
  };
  
  // Check endpoints
  log.header('🌐 Endpoint Availability Check');
  for (const endpoint of config.endpoints) {
    const result = await checkEndpoint(endpoint);
    results.endpoints[endpoint] = result;
  }
  
  // Check security headers
  results.securityHeaders = await checkSecurityHeaders();
  
  // Check performance
  results.performance = await checkPerformance();
  
  // Check SSL
  results.ssl = await checkSSL();
  
  // Calculate overall result
  const endpointsPassed = Object.values(results.endpoints).every(r => r.success);
  results.overall = endpointsPassed && results.securityHeaders && results.performance && results.ssl;
  
  // Summary
  log.header('📊 Health Check Summary');
  log.info(`Endpoints: ${endpointsPassed ? 'PASS' : 'FAIL'}`);
  log.info(`Security Headers: ${results.securityHeaders ? 'PASS' : 'FAIL'}`);
  log.info(`Performance: ${results.performance ? 'PASS' : 'FAIL'}`);
  log.info(`SSL Certificate: ${results.ssl ? 'PASS' : 'FAIL'}`);
  
  console.log(`\n${results.overall ? colors.green : colors.red}Overall Status: ${results.overall ? 'HEALTHY' : 'UNHEALTHY'}${colors.reset}`);
  console.log(`Completed at: ${new Date().toISOString()}`);
  
  // Exit with appropriate code
  process.exit(results.overall ? 0 : 1);
}

// Run the health check
if (require.main === module) {
  runHealthCheck().catch((error) => {
    log.error(`Health check failed: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { runHealthCheck };
