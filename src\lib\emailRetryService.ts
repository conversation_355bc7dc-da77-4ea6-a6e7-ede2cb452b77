/**
 * Email Retry Service
 * Handles failed email retries with exponential backoff
 */

import { supabase } from '@/integrations/supabase/client'
import { sendEmail, type EmailRequest } from './emailService'
import { logger } from '@/lib/logger'

export interface RetryQueueItem {
  retry_id: string
  delivery_id: string
  org_id: string
  email_type: string
  recipient: string
  retry_count: number
  original_data: Record<string, unknown>
}

export interface RetryResult {
  success: boolean
  retry_id: string
  delivery_id: string
  error?: string
}

export interface RetryStats {
  pending: number
  processing: number
  completed: number
  failed: number
  total: number
}

/**
 * Email Retry Service Class
 */
export class EmailRetryService {
  private isProcessing = false
  private batchSize = 10
  private processingInterval: NodeJS.Timeout | null = null

  constructor(batchSize = 10) {
    this.batchSize = batchSize
  }

  /**
   * Start the retry processor
   */
  start(intervalMs = 60000): void {
    if (this.processingInterval) {
      this.stop()
    }

    this.processingInterval = setInterval(() => {
      this.processRetryQueue().catch(error => {
        logger.error('Error in retry queue processor', {
          component: 'EmailRetryService',
          action: 'processRetryQueue',
          error: error.message
        })
      })
    }, intervalMs)

    logger.info('Email retry service started', {
      component: 'EmailRetryService',
      action: 'start',
      metadata: { intervalMs, batchSize: this.batchSize }
    })
  }

  /**
   * Stop the retry processor
   */
  stop(): void {
    if (this.processingInterval) {
      clearInterval(this.processingInterval)
      this.processingInterval = null
    }

    logger.info('Email retry service stopped', {
      component: 'EmailRetryService',
      action: 'stop'
    })
  }

  /**
   * Process retry queue
   */
  async processRetryQueue(): Promise<RetryResult[]> {
    if (this.isProcessing) {
      logger.debug('Retry queue already processing, skipping', {
        component: 'EmailRetryService',
        action: 'processRetryQueue'
      })
      return []
    }

    this.isProcessing = true
    const results: RetryResult[] = []

    try {
      // Get emails to retry
      const { data: retryItems, error } = await supabase.rpc('get_emails_to_retry', {
        batch_size: this.batchSize
      })

      if (error) {
        throw error
      }

      if (!retryItems || retryItems.length === 0) {
        logger.debug('No emails to retry', {
          component: 'EmailRetryService',
          action: 'processRetryQueue'
        })
        return []
      }

      logger.info('Processing retry queue', {
        component: 'EmailRetryService',
        action: 'processRetryQueue',
        metadata: { itemCount: retryItems.length }
      })

      // Process each retry item
      for (const item of retryItems) {
        const result = await this.retryEmail(item)
        results.push(result)
      }

      logger.info('Retry queue processing completed', {
        component: 'EmailRetryService',
        action: 'processRetryQueue',
        metadata: {
          total: results.length,
          successful: results.filter(r => r.success).length,
          failed: results.filter(r => !r.success).length
        }
      })

    } catch (error) {
      logger.error('Error processing retry queue', {
        component: 'EmailRetryService',
        action: 'processRetryQueue',
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    } finally {
      this.isProcessing = false
    }

    return results
  }

  /**
   * Retry a single email
   */
  async retryEmail(item: RetryQueueItem): Promise<RetryResult> {
    try {
      logger.info('Retrying email', {
        component: 'EmailRetryService',
        action: 'retryEmail',
        metadata: {
          retryId: item.retry_id,
          deliveryId: item.delivery_id,
          recipient: item.recipient,
          retryCount: item.retry_count
        }
      })

      // Reconstruct email request from original data
      const emailRequest: EmailRequest = {
        type: item.email_type as any,
        to: item.recipient,
        data: item.original_data,
        org_id: item.org_id
      }

      // Attempt to send email
      const response = await sendEmail(emailRequest)

      if (response.success) {
        // Mark retry as successful
        await this.completeRetry(item.retry_id, true)
        
        logger.info('Email retry successful', {
          component: 'EmailRetryService',
          action: 'retryEmail',
          metadata: {
            retryId: item.retry_id,
            deliveryId: item.delivery_id,
            recipient: item.recipient
          }
        })

        return {
          success: true,
          retry_id: item.retry_id,
          delivery_id: item.delivery_id
        }
      } else {
        // Mark retry as failed
        await this.completeRetry(item.retry_id, false, response.error)
        
        logger.warn('Email retry failed', {
          component: 'EmailRetryService',
          action: 'retryEmail',
          metadata: {
            retryId: item.retry_id,
            deliveryId: item.delivery_id,
            recipient: item.recipient,
            error: response.error
          }
        })

        return {
          success: false,
          retry_id: item.retry_id,
          delivery_id: item.delivery_id,
          error: response.error
        }
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      
      // Mark retry as failed
      await this.completeRetry(item.retry_id, false, errorMessage)
      
      logger.error('Email retry error', {
        component: 'EmailRetryService',
        action: 'retryEmail',
        metadata: {
          retryId: item.retry_id,
          deliveryId: item.delivery_id,
          recipient: item.recipient
        },
        error: errorMessage
      })

      return {
        success: false,
        retry_id: item.retry_id,
        delivery_id: item.delivery_id,
        error: errorMessage
      }
    }
  }

  /**
   * Complete a retry attempt
   */
  private async completeRetry(retryId: string, success: boolean, errorMessage?: string): Promise<void> {
    try {
      const { error } = await supabase.rpc('complete_retry', {
        retry_id_param: retryId,
        success,
        error_message: errorMessage || null
      })

      if (error) {
        throw error
      }
    } catch (error) {
      logger.error('Error completing retry', {
        component: 'EmailRetryService',
        action: 'completeRetry',
        metadata: { retryId, success, errorMessage },
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * Add email to retry queue
   */
  async addToRetryQueue(
    deliveryId: string,
    orgId: string,
    emailType: string,
    recipient: string,
    originalData: Record<string, unknown>,
    priority = 1
  ): Promise<string | null> {
    try {
      const { data, error } = await supabase.rpc('add_to_retry_queue', {
        delivery_id_param: deliveryId,
        org_id_param: orgId,
        email_type_param: emailType,
        recipient_param: recipient,
        original_data_param: originalData,
        priority_param: priority
      })

      if (error) {
        throw error
      }

      logger.info('Email added to retry queue', {
        component: 'EmailRetryService',
        action: 'addToRetryQueue',
        metadata: {
          deliveryId,
          recipient,
          emailType,
          priority
        }
      })

      return data
    } catch (error) {
      logger.error('Error adding email to retry queue', {
        component: 'EmailRetryService',
        action: 'addToRetryQueue',
        metadata: { deliveryId, recipient, emailType },
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      return null
    }
  }

  /**
   * Get retry queue statistics
   */
  async getRetryStats(orgId?: string): Promise<RetryStats> {
    try {
      let query = supabase
        .from('email_retry_queue')
        .select('status')

      if (orgId) {
        query = query.eq('org_id', orgId)
      }

      const { data, error } = await query

      if (error) {
        throw error
      }

      const stats = data.reduce((acc, item) => {
        acc[item.status as keyof RetryStats]++
        acc.total++
        return acc
      }, {
        pending: 0,
        processing: 0,
        completed: 0,
        failed: 0,
        total: 0
      } as RetryStats)

      return stats
    } catch (error) {
      logger.error('Error getting retry stats', {
        component: 'EmailRetryService',
        action: 'getRetryStats',
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      
      return {
        pending: 0,
        processing: 0,
        completed: 0,
        failed: 0,
        total: 0
      }
    }
  }

  /**
   * Clean up old retry queue entries
   */
  async cleanupRetryQueue(daysOld = 7): Promise<number> {
    try {
      const { data, error } = await supabase.rpc('cleanup_retry_queue', {
        days_old: daysOld
      })

      if (error) {
        throw error
      }

      logger.info('Retry queue cleanup completed', {
        component: 'EmailRetryService',
        action: 'cleanupRetryQueue',
        metadata: { deletedCount: data, daysOld }
      })

      return data || 0
    } catch (error) {
      logger.error('Error cleaning up retry queue', {
        component: 'EmailRetryService',
        action: 'cleanupRetryQueue',
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      return 0
    }
  }
}

// Export singleton instance
export const emailRetryService = new EmailRetryService()
