import { useState } from 'react'
import { format } from 'date-fns'
import { useAuth } from '@/hooks/useAuthHook'
import { supabase } from '@/lib/supabase'
import { useToast } from '@/hooks/use-toast'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Separator } from '@/components/ui/separator'
import { LoadingSpinner } from '@/components/ui/loading'
import { 
  Building, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar, 
  FileText, 
  DollarSign,
  Plus,
  Receipt
} from 'lucide-react'
import { useBillsByVendor, usePaymentsByEntity, useCreateBill } from '@/hooks/queries'
import { BillForm } from '@/components/bills/BillForm'
import { PaymentForm } from '@/components/payments/PaymentForm'
import { useActiveVendors, useActiveAccounts, useActiveWithholdingTaxRates } from '@/hooks/queries'
import type { Vendor } from '@/types/database'
import type { BillFormData, BillLineData } from '@/types/bills'

interface VendorDetailsModalProps {
  vendor: Vendor | null
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function VendorDetailsModal({
  vendor,
  open,
  onOpenChange
}: VendorDetailsModalProps) {
  const { profile } = useAuth()
  const { toast } = useToast()
  const createBill = useCreateBill()
  const [showBillDialog, setShowBillDialog] = useState(false)
  const [showPaymentDialog, setShowPaymentDialog] = useState(false)

  // Fetch vendor's bills and payments
  const {
    data: bills = [],
    isLoading: billsLoading
  } = useBillsByVendor(vendor?.id)

  const {
    data: payments = [],
    isLoading: paymentsLoading
  } = usePaymentsByEntity('vendor', vendor?.id)

  // Fetch data for bill form
  const { data: vendors = [] } = useActiveVendors()
  const { data: accounts = [] } = useActiveAccounts()
  const { data: withholdingRates = [] } = useActiveWithholdingTaxRates()

  if (!vendor) return null

  const handleBillSubmit = async (formData: BillFormData, billLines: BillLineData[]) => {
    if (!profile?.org_id) return

    try {
      // Calculate totals
      const totals = billLines.reduce((acc, line) => {
        const lineTotal = line.quantity * line.unit_price
        const lineTax = lineTotal * (line.tax_rate_pct / 100)
        return {
          subtotal: acc.subtotal + lineTotal,
          taxAmount: acc.taxAmount + lineTax
        }
      }, { subtotal: 0, taxAmount: 0 })

      // Generate bill number
      const date = new Date()
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const timestamp = Date.now().toString().slice(-4)
      const billNumber = formData.bill_number || `BILL-${year}${month}-${timestamp}`

      const billData = {
        vendor_id: formData.vendor_id,
        bill_number: billNumber,
        date_issued: formData.date_issued,
        due_date: formData.due_date,
        withholding_tax_rate_id: formData.withholding_tax_rate_id || null,
        notes: formData.notes,
        status: formData.status,
        total_amount: totals.subtotal + totals.taxAmount,
        tax_amount: totals.taxAmount,
        lines: billLines.map(line => ({
          account_id: formData.account_id && formData.account_id !== '' ? formData.account_id : null,
          description: `${line.item}${line.description ? ' - ' + line.description : ''}`,
          quantity: line.quantity,
          unit_price: line.unit_price,
          tax_rate_pct: line.tax_rate_pct,
          line_total: line.quantity * line.unit_price,
          tax_amount: (line.quantity * line.unit_price) * (line.tax_rate_pct / 100)
        }))
      }

      await createBill.mutateAsync(billData)

      setShowBillDialog(false)
      // React Query will automatically refetch bills
    } catch (error) {
      console.error('Error creating bill:', error)
      // Error handling is done by the React Query hook
    }
  }

  const handlePaymentSuccess = () => {
    setShowPaymentDialog(false)
    // React Query will automatically refetch payments
  }

  const formatCurrency = (amount: number) => {
    return `UGX ${amount.toLocaleString()}`
  }

  const getStatusBadge = (status: string) => {
    const statusColors = {
      draft: 'bg-gray-100 text-gray-800',
      approved: 'bg-blue-100 text-blue-800',
      paid: 'bg-green-100 text-green-800',
      overdue: 'bg-red-100 text-red-800',
      cancelled: 'bg-gray-100 text-gray-800',
    }
    
    return (
      <Badge className={statusColors[status as keyof typeof statusColors] || 'bg-gray-100 text-gray-800'}>
        {status}
      </Badge>
    )
  }

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-scroll">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Building className="h-5 w-5" />
              Vendor Details - {vendor.name}
            </DialogTitle>
          </DialogHeader>
          
          <div className="space-y-6">
            {/* Vendor Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building className="h-4 w-4" />
                  Vendor Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Name</label>
                      <p className="font-medium">{vendor.name}</p>
                    </div>
                    {vendor.email && (
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4 text-gray-400" />
                        <span className="text-sm">{vendor.email}</span>
                      </div>
                    )}
                    {vendor.phone && (
                      <div className="flex items-center gap-2">
                        <Phone className="h-4 w-4 text-gray-400" />
                        <span className="text-sm">{vendor.phone}</span>
                      </div>
                    )}
                  </div>
                  <div className="space-y-3">
                    {vendor.address && (
                      <div className="flex items-start gap-2">
                        <MapPin className="h-4 w-4 text-gray-400 mt-0.5" />
                        <span className="text-sm">{vendor.address}</span>
                      </div>
                    )}
                    <div>
                      <label className="text-sm font-medium text-gray-500">Payment Terms</label>
                      <p className="text-sm">{vendor.payment_terms} days</p>
                    </div>
                    {vendor.tin_number && (
                      <div>
                        <label className="text-sm font-medium text-gray-500">TIN Number</label>
                        <p className="text-sm">{vendor.tin_number}</p>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Action Buttons */}
            <div className="flex gap-3">
              <Button 
                onClick={() => setShowBillDialog(true)}
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Add Bill
              </Button>
              <Button 
                variant="outline"
                onClick={() => setShowPaymentDialog(true)}
                className="flex items-center gap-2"
              >
                <DollarSign className="h-4 w-4" />
                Add Payment
              </Button>
            </div>

            <Separator />

            {/* Documents Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Associated Documents</h3>
              
              {/* Bills */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    Bills ({bills.length})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {billsLoading ? (
                    <LoadingSpinner size="sm" text="Loading bills..." />
                  ) : bills.length > 0 ? (
                    <div className="overflow-x-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Bill #</TableHead>
                            <TableHead>Date</TableHead>
                            <TableHead>Due Date</TableHead>
                            <TableHead>Amount</TableHead>
                            <TableHead>Status</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {bills.map((bill) => (
                            <TableRow key={bill.id}>
                              <TableCell className="font-medium">
                                {bill.bill_number}
                              </TableCell>
                              <TableCell>
                                {format(new Date(bill.date_issued), 'MMM dd, yyyy')}
                              </TableCell>
                              <TableCell>
                                {format(new Date(bill.due_date), 'MMM dd, yyyy')}
                              </TableCell>
                              <TableCell>{formatCurrency(bill.total_amount)}</TableCell>
                              <TableCell>{getStatusBadge(bill.status)}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  ) : (
                    <p className="text-gray-500 text-center py-4">No bills found</p>
                  )}
                </CardContent>
              </Card>

              {/* Payments */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Receipt className="h-4 w-4" />
                    Payments ({payments.length})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {paymentsLoading ? (
                    <LoadingSpinner size="sm" text="Loading payments..." />
                  ) : payments.length > 0 ? (
                    <div className="overflow-x-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Payment #</TableHead>
                            <TableHead>Date</TableHead>
                            <TableHead>Amount</TableHead>
                            <TableHead>Method</TableHead>
                            <TableHead>Reference</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {payments.map((payment) => (
                            <TableRow key={payment.id}>
                              <TableCell className="font-medium">
                                {payment.payment_number}
                              </TableCell>
                              <TableCell>
                                {format(new Date(payment.payment_date), 'MMM dd, yyyy')}
                              </TableCell>
                              <TableCell>{formatCurrency(payment.amount)}</TableCell>
                              <TableCell className="capitalize">{payment.payment_method}</TableCell>
                              <TableCell>{payment.reference || '-'}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  ) : (
                    <p className="text-gray-500 text-center py-4">No payments found</p>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Bill Form */}
      <BillForm
        open={showBillDialog}
        onOpenChange={setShowBillDialog}
        editingBill={null}
        vendors={vendors}
        accounts={accounts}
        withholdingRates={withholdingRates}
        onSubmit={handleBillSubmit}
        preselectedVendorId={vendor.id}
      />

      {/* Payment Dialog */}
      <PaymentForm
        open={showPaymentDialog}
        onOpenChange={setShowPaymentDialog}
        onSuccess={handlePaymentSuccess}
        preselectedPayeeType="vendor"
        preselectedPayeeId={vendor.id}
      />
    </>
  )
}
