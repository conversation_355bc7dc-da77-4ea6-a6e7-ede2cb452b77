import { PricingValidation, SkuGenerationOptions } from './types'

// SKU generation utilities
export function generateSkuSuggestions(
  productName: string,
  categoryId?: string,
  options: Partial<SkuGenerationOptions> = {}
): string[] {
  const suggestions: string[] = []
  
  if (!productName) return suggestions

  const {
    includeDate = true,
    includeRandom = true,
    length = 8
  } = options

  // Clean product name for SKU
  const cleanName = productName
    .toUpperCase()
    .replace(/[^A-Z0-9]/g, '')
    .substring(0, 4)

  // Category prefix (if available)
  const categoryPrefix = categoryId ? categoryId.substring(0, 2).toUpperCase() : ''

  // Date suffix
  const dateSuffix = includeDate ? new Date().getFullYear().toString().slice(-2) : ''

  // Random suffix
  const randomSuffix = includeRandom ? Math.floor(Math.random() * 1000).toString().padStart(3, '0') : ''

  // Generate different variations
  if (categoryPrefix) {
    suggestions.push(`${categoryPrefix}-${cleanName}`)
    if (dateSuffix) {
      suggestions.push(`${categoryPrefix}-${cleanName}-${dateSuffix}`)
    }
    if (randomSuffix) {
      suggestions.push(`${categoryPrefix}-${cleanName}-${randomSuffix}`)
    }
  }

  suggestions.push(cleanName)
  if (dateSuffix) {
    suggestions.push(`${cleanName}-${dateSuffix}`)
  }
  if (randomSuffix) {
    suggestions.push(`${cleanName}-${randomSuffix}`)
  }

  // Ensure unique suggestions and limit length
  return [...new Set(suggestions)]
    .map(sku => sku.substring(0, length))
    .slice(0, 5)
}

// Pricing validation utilities
export function validatePricing(costPrice: number, sellingPrice: number): PricingValidation {
  const errors: string[] = []
  const warnings: string[] = []

  // Calculate margins
  const marginPercentage = sellingPrice > 0 
    ? ((sellingPrice - costPrice) / sellingPrice) * 100 
    : 0
  
  const markupPercentage = costPrice > 0 
    ? ((sellingPrice - costPrice) / costPrice) * 100 
    : 0

  // Validation rules
  if (sellingPrice < costPrice && costPrice > 0 && sellingPrice > 0) {
    errors.push('Selling price cannot be lower than cost price')
  }

  if (marginPercentage < 10 && sellingPrice >= costPrice && costPrice > 0) {
    warnings.push('Very low profit margin (less than 10%)')
  } else if (marginPercentage < 20 && sellingPrice >= costPrice && costPrice > 0) {
    warnings.push('Low profit margin (less than 20%)')
  }

  if (marginPercentage > 80 && costPrice > 0) {
    warnings.push('Very high profit margin (over 80%) - consider market competitiveness')
  }

  return {
    errors,
    warnings,
    marginPercentage: Math.round(marginPercentage * 100) / 100,
    markupPercentage: Math.round(markupPercentage * 100) / 100,
  }
}

// Form validation utilities
export function validateSku(sku: string, existingSkus: string[] = []): string[] {
  const errors: string[] = []

  if (!sku) {
    errors.push('SKU is required')
    return errors
  }

  if (sku.length < 2) {
    errors.push('SKU must be at least 2 characters long')
  }

  if (sku.length > 20) {
    errors.push('SKU must be less than 20 characters')
  }

  if (!/^[A-Z0-9\-_]+$/i.test(sku)) {
    errors.push('SKU can only contain letters, numbers, hyphens, and underscores')
  }

  if (existingSkus.includes(sku.toUpperCase())) {
    errors.push('SKU already exists')
  }

  return errors
}

export function validateProductName(name: string): string[] {
  const errors: string[] = []

  if (!name) {
    errors.push('Product name is required')
    return errors
  }

  if (name.length < 2) {
    errors.push('Product name must be at least 2 characters long')
  }

  if (name.length > 100) {
    errors.push('Product name must be less than 100 characters')
  }

  return errors
}

// Image validation utilities
export function validateImage(file: File): string[] {
  const errors: string[] = []
  const maxSize = 10 * 1024 * 1024 // 10MB
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']

  if (file.size > maxSize) {
    errors.push('Image size must be less than 10MB')
  }

  if (!allowedTypes.includes(file.type)) {
    errors.push('Image must be JPEG, PNG, GIF, or WebP format')
  }

  return errors
}

// Formatting utilities
export function formatCurrency(amount: number, currency = 'UGX'): string {
  return new Intl.NumberFormat('en-UG', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  }).format(amount)
}

export function formatPercentage(value: number, decimals = 1): string {
  return `${value.toFixed(decimals)}%`
}

// Form data transformation utilities
export function transformFormDataForSubmission(formData: any) {
  return {
    ...formData,
    description: formData.description || '',
    category_id: formData.category_id === '__none__' || !formData.category_id ? null : formData.category_id,
    barcode: formData.barcode || '',
    dimensions: formData.dimensions || '',
    weight: formData.weight || null,
  }
}

export function transformProductForForm(product: any) {
  return {
    ...product,
    category_id: product.category_id || '__none__',
    initial_quantity: 0, // Don't show existing stock as initial quantity when editing
    cost_price: Number(product.cost_price) || 0,
    selling_price: Number(product.selling_price) || 0,
    reorder_level: Number(product.reorder_level) || 0,
    reorder_quantity: Number(product.reorder_quantity) || 0,
    weight: product.weight ? Number(product.weight) : null,
    track_inventory: product.track_inventory ?? true,
    is_active: product.is_active ?? true,
    is_sellable: product.is_sellable ?? true,
    is_purchasable: product.is_purchasable ?? true,
  }
}
