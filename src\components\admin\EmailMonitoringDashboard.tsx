/**
 * Email Monitoring Dashboard
 * Displays email system health, rate limits, and bounce statistics
 */

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { supabase } from '@/integrations/supabase/client'
import { useToast } from '@/hooks/use-toast'
import { 
  Mail, 
  AlertTriangle, 
  TrendingUp, 
  Shield, 
  Clock, 
  BarChart3,
  RefreshCw,
  Settings
} from 'lucide-react'

interface EmailStats {
  total_sent: number
  total_failed: number
  total_bounced: number
  total_rate_limited: number
  success_rate: number
  daily_breakdown: Record<string, number>
  hourly_pattern: Record<string, number>
}

interface RateLimitConfig {
  per_minute: number
  per_hour: number
  per_day: number
  burst_limit: number
  alert_threshold_percentage: number
  alert_email: string
}

interface RateLimitAlert {
  id: string
  alert_type: string
  threshold_percentage: number
  current_usage: number
  limit_value: number
  time_window: string
  message: string
  resolved: boolean
  created_at: string
}

export function EmailMonitoringDashboard() {
  const [emailStats, setEmailStats] = useState<EmailStats | null>(null)
  const [rateLimitConfig, setRateLimitConfig] = useState<RateLimitConfig | null>(null)
  const [alerts, setAlerts] = useState<RateLimitAlert[]>([])
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    try {
      setLoading(true)
      
      // Get current user's organization
      const { data: profile } = await supabase
        .from('profiles')
        .select('org_id')
        .eq('id', (await supabase.auth.getUser()).data.user?.id)
        .single()

      if (!profile?.org_id) {
        throw new Error('Organization not found')
      }

      // Load email analytics
      const { data: analytics, error: analyticsError } = await supabase.rpc('get_email_analytics', {
        org_id_param: profile.org_id,
        start_date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        end_date: new Date().toISOString().split('T')[0]
      })

      if (analyticsError) throw analyticsError
      setEmailStats(analytics)

      // Load rate limit configuration
      const { data: config, error: configError } = await supabase.rpc('get_rate_limit_config', {
        org_id_param: profile.org_id
      })

      if (configError) throw configError
      setRateLimitConfig(config)

      // Load recent alerts
      const { data: alertsData, error: alertsError } = await supabase
        .from('email_rate_limit_alerts')
        .select('*')
        .eq('org_id', profile.org_id)
        .order('created_at', { ascending: false })
        .limit(10)

      if (alertsError) throw alertsError
      setAlerts(alertsData || [])

    } catch (error) {
      console.error('Error loading dashboard data:', error)
      toast({
        title: 'Error',
        description: 'Failed to load email monitoring data',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const refreshData = async () => {
    setRefreshing(true)
    await loadDashboardData()
    setRefreshing(false)
    toast({
      title: 'Success',
      description: 'Dashboard data refreshed'
    })
  }

  const resolveAlert = async (alertId: string) => {
    try {
      const { error } = await supabase
        .from('email_rate_limit_alerts')
        .update({ 
          resolved: true, 
          resolved_at: new Date().toISOString(),
          resolved_by: (await supabase.auth.getUser()).data.user?.id
        })
        .eq('id', alertId)

      if (error) throw error

      setAlerts(alerts.map(alert => 
        alert.id === alertId 
          ? { ...alert, resolved: true }
          : alert
      ))

      toast({
        title: 'Success',
        description: 'Alert resolved'
      })
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to resolve alert',
        variant: 'destructive'
      })
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  const unresolvedAlerts = alerts.filter(alert => !alert.resolved)
  const currentHourUsage = emailStats?.hourly_pattern?.[new Date().getHours().toString()] || 0
  const hourlyUsagePercentage = rateLimitConfig ? (currentHourUsage / rateLimitConfig.per_hour) * 100 : 0

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Email Monitoring</h1>
          <p className="text-muted-foreground">Monitor email system health and performance</p>
        </div>
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            onClick={refreshData}
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline">
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Button>
        </div>
      </div>

      {/* Alerts */}
      {unresolvedAlerts.length > 0 && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            You have {unresolvedAlerts.length} unresolved email alerts that need attention.
          </AlertDescription>
        </Alert>
      )}

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Emails Sent (7 days)</CardTitle>
            <Mail className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{emailStats?.total_sent || 0}</div>
            <p className="text-xs text-muted-foreground">
              Success rate: {emailStats?.success_rate || 0}%
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Failed Emails</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{emailStats?.total_failed || 0}</div>
            <p className="text-xs text-muted-foreground">
              Bounced: {emailStats?.total_bounced || 0}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Rate Limited</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{emailStats?.total_rate_limited || 0}</div>
            <p className="text-xs text-muted-foreground">
              This hour: {currentHourUsage}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Hourly Usage</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{currentHourUsage}</div>
            <Progress value={hourlyUsagePercentage} className="mt-2" />
            <p className="text-xs text-muted-foreground mt-1">
              {hourlyUsagePercentage.toFixed(1)}% of hourly limit
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="rate-limits">Rate Limits</TabsTrigger>
          <TabsTrigger value="alerts">Alerts</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Current Rate Limits</CardTitle>
                <CardDescription>Active rate limiting configuration</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                {rateLimitConfig && (
                  <>
                    <div className="flex justify-between">
                      <span>Per Minute:</span>
                      <Badge variant="outline">{rateLimitConfig.per_minute}</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Per Hour:</span>
                      <Badge variant="outline">{rateLimitConfig.per_hour}</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Per Day:</span>
                      <Badge variant="outline">{rateLimitConfig.per_day}</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Burst Limit:</span>
                      <Badge variant="outline">{rateLimitConfig.burst_limit}</Badge>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Recent Alerts</CardTitle>
                <CardDescription>Latest rate limit and bounce alerts</CardDescription>
              </CardHeader>
              <CardContent>
                {alerts.slice(0, 5).map((alert) => (
                  <div key={alert.id} className="flex items-center justify-between py-2 border-b last:border-b-0">
                    <div className="flex-1">
                      <p className="text-sm font-medium">{alert.message}</p>
                      <p className="text-xs text-muted-foreground">
                        {new Date(alert.created_at).toLocaleDateString()}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant={alert.resolved ? "secondary" : "destructive"}>
                        {alert.resolved ? "Resolved" : "Active"}
                      </Badge>
                      {!alert.resolved && (
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => resolveAlert(alert.id)}
                        >
                          Resolve
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
                {alerts.length === 0 && (
                  <p className="text-sm text-muted-foreground">No alerts found</p>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="rate-limits">
          <Card>
            <CardHeader>
              <CardTitle>Rate Limit Configuration</CardTitle>
              <CardDescription>Manage email sending rate limits</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Rate limit configuration management will be available in a future update.
              </p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="alerts">
          <Card>
            <CardHeader>
              <CardTitle>Alert Management</CardTitle>
              <CardDescription>View and manage all email alerts</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {alerts.map((alert) => (
                  <div key={alert.id} className="p-4 border rounded-lg">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h4 className="font-medium">{alert.alert_type.replace('_', ' ').toUpperCase()}</h4>
                        <p className="text-sm text-muted-foreground mt-1">{alert.message}</p>
                        <p className="text-xs text-muted-foreground mt-2">
                          {new Date(alert.created_at).toLocaleString()}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant={alert.resolved ? "secondary" : "destructive"}>
                          {alert.resolved ? "Resolved" : "Active"}
                        </Badge>
                        {!alert.resolved && (
                          <Button 
                            size="sm" 
                            variant="outline"
                            onClick={() => resolveAlert(alert.id)}
                          >
                            Resolve
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
                {alerts.length === 0 && (
                  <p className="text-sm text-muted-foreground">No alerts found</p>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics">
          <Card>
            <CardHeader>
              <CardTitle>Email Analytics</CardTitle>
              <CardDescription>Detailed email sending analytics and trends</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Advanced analytics dashboard will be available in a future update.
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
