import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog'
import { Plus } from 'lucide-react'
import { ProductLineItem } from '@/components/inventory/ProductLineItem'
import { useInventoryBudgetIntegration } from '@/hooks/useInventoryBudgetIntegration'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { AlertTriangle, DollarSign } from 'lucide-react'
import type {
  PurchaseOrderFormProps,
  PurchaseOrderFormData,
  PurchaseOrderLineData
} from '@/types/purchase-orders'

const initialFormData: PurchaseOrderFormData = {
  vendor_id: '',
  po_number: '',
  date_issued: new Date().toISOString().split('T')[0],
  expected_delivery_date: '',
  notes: '',
  terms_and_conditions: '',
  delivery_address: '',
  status: 'draft',
}

const initialLineData: PurchaseOrderLineData = {
  product_id: '',
  item: '',
  description: '',
  quantity: 1,
  unit_price: 0,
  tax_rate_pct: 0,
}

export function PurchaseOrderForm({
  open,
  onOpenChange,
  editingPO,
  vendors,
  onSubmit,
  preselectedVendorId
}: PurchaseOrderFormProps) {
  const [formData, setFormData] = useState<PurchaseOrderFormData>(
    editingPO ? {
      vendor_id: editingPO.vendor_id,
      po_number: editingPO.po_number,
      date_issued: editingPO.date_issued,
      expected_delivery_date: editingPO.expected_delivery_date || '',
      notes: editingPO.notes || '',
      terms_and_conditions: editingPO.terms_and_conditions || '',
      delivery_address: editingPO.delivery_address || '',
      status: editingPO.status,
    } : {
      ...initialFormData,
      vendor_id: preselectedVendorId || ''
    }
  )
  const [poLines, setPOLines] = useState<PurchaseOrderLineData[]>(
    editingPO ? [] : [{ ...initialLineData }]
  )
  const [submitting, setSubmitting] = useState(false)
  const { validateInventoryPurchase } = useInventoryBudgetIntegration()
  const [budgetValidation, setBudgetValidation] = useState<any>(null)

  // Load existing PO lines when editing
  useEffect(() => {
    if (editingPO?.lines) {
      const formLines: PurchaseOrderLineData[] = editingPO.lines.map(line => ({
        product_id: line.product_id || '',
        item: line.item,
        description: line.description || '',
        quantity: line.quantity,
        unit_price: line.unit_price,
        tax_rate_pct: line.tax_rate_pct
      }))
      setPOLines(formLines)
    } else {
      setPOLines([{ ...initialLineData }])
    }
  }, [editingPO])

  const addPOLine = () => {
    setPOLines([...poLines, { ...initialLineData }])
  }

  const updatePOLine = (index: number, field: keyof PurchaseOrderLineData, value: string | number) => {
    const updatedLines = [...poLines]
    updatedLines[index] = { ...updatedLines[index], [field]: value }
    setPOLines(updatedLines)
  }

  const removePOLine = (index: number) => {
    setPOLines(poLines.filter((_, i) => i !== index))
  }

  const calculateLineTotals = () => {
    let subtotal = 0
    let taxAmount = 0

    poLines.forEach(line => {
      const lineTotal = line.quantity * line.unit_price
      subtotal += lineTotal
      taxAmount += lineTotal * (line.tax_rate_pct / 100)
    })

    return {
      subtotal,
      taxAmount,
      totalAmount: subtotal + taxAmount
    }
  }

  const generatePONumber = () => {
    const timestamp = Date.now().toString().slice(-6)
    return `PO-${timestamp}`
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSubmitting(true)
    try {
      const submitData = {
        ...formData,
        po_number: formData.po_number || generatePONumber()
      }
      await onSubmit(submitData, poLines)
      setFormData(initialFormData)
      setPOLines([{ ...initialLineData }])
    } finally {
      setSubmitting(false)
    }
  }

  const totals = calculateLineTotals()

  // Validate budget when totals change
  useEffect(() => {
    if (totals.totalAmount > 0 && formData.vendor_id) {
      // For now, we'll use a default inventory asset account
      // In a real implementation, this would be configurable
      const validateBudget = async () => {
        try {
          // This is a simplified validation - in practice you'd need to determine
          // the correct account based on the organization's account mappings
          const validation = await validateInventoryPurchase({
            quantity: 1,
            unitCost: totals.totalAmount,
            accountId: 'default-inventory-account' // This should be dynamic
          })
          setBudgetValidation(validation)
        } catch (error) {
          console.error('Budget validation failed:', error)
          setBudgetValidation(null)
        }
      }
      validateBudget()
    } else {
      setBudgetValidation(null)
    }
  }, [totals.totalAmount, formData.vendor_id, validateInventoryPurchase])

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-scroll">
        <DialogHeader>
          <DialogTitle>
            {editingPO ? 'Edit Purchase Order' : 'Create New Purchase Order'}
          </DialogTitle>
          <DialogDescription>
            {editingPO ? 'Update the purchase order details' : 'Create a new purchase order for a vendor'}
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="vendor_id">Vendor *</Label>
              <Select value={formData.vendor_id || ''} onValueChange={(value) => setFormData({ ...formData, vendor_id: value })}>
                <SelectTrigger>
                  <SelectValue placeholder="Select vendor" />
                </SelectTrigger>
                <SelectContent>
                  {vendors.map((vendor) => (
                    <SelectItem key={vendor.id} value={vendor.id}>
                      {vendor.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="po_number">PO Number</Label>
              <Input
                id="po_number"
                value={formData.po_number}
                onChange={(e) => setFormData({ ...formData, po_number: e.target.value })}
                placeholder="Auto-generated if empty"
              />
            </div>

            <div>
              <Label htmlFor="date_issued">Date Issued *</Label>
              <Input
                id="date_issued"
                type="date"
                value={formData.date_issued}
                onChange={(e) => setFormData({ ...formData, date_issued: e.target.value })}
              />
            </div>

            <div>
              <Label htmlFor="expected_delivery_date">Expected Delivery Date</Label>
              <Input
                id="expected_delivery_date"
                type="date"
                value={formData.expected_delivery_date}
                onChange={(e) => setFormData({ ...formData, expected_delivery_date: e.target.value })}
              />
            </div>

            <div>
              <Label htmlFor="status">Status</Label>
              <Select value={formData.status} onValueChange={(value) => setFormData({ ...formData, status: value as PurchaseOrderFormData['status'] })}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="sent">Sent</SelectItem>
                  <SelectItem value="confirmed">Confirmed</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="delivery_address">Delivery Address</Label>
              <Input
                id="delivery_address"
                value={formData.delivery_address}
                onChange={(e) => setFormData({ ...formData, delivery_address: e.target.value })}
                placeholder="Delivery address"
              />
            </div>
          </div>

          <div>
            <Label htmlFor="terms_and_conditions">Terms and Conditions</Label>
            <Textarea
              id="terms_and_conditions"
              value={formData.terms_and_conditions}
              onChange={(e) => setFormData({ ...formData, terms_and_conditions: e.target.value })}
              placeholder="Terms and conditions"
              rows={3}
            />
          </div>

          <div>
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
              placeholder="Additional notes for the purchase order"
              rows={3}
            />
          </div>

          <div>
            <div className="flex items-center justify-between mb-4">
              <Label>Purchase Order Lines</Label>
              <Button type="button" variant="outline" onClick={addPOLine}>
                <Plus className="h-4 w-4 mr-2" />
                Add Line
              </Button>
            </div>

            <div className="space-y-4">
              {poLines.map((line, index) => (
                <ProductLineItem
                  key={index}
                  line={line}
                  index={index}
                  onUpdate={updatePOLine}
                  onRemove={removePOLine}
                  isLast={poLines.length === 1}
                  showProductSelection={true}
                />
              ))}
            </div>

            {poLines.length > 0 && (
              <div className="mt-4 p-4 bg-gray-50 rounded">
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Subtotal:</span>
                    <span>UGX {totals.subtotal.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Tax:</span>
                    <span>UGX {totals.taxAmount.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between font-bold border-t pt-2">
                    <span>Total:</span>
                    <span>UGX {totals.totalAmount.toLocaleString()}</span>
                  </div>
                </div>
              </div>
            )}

            {/* Budget Validation Alert */}
            {budgetValidation && budgetValidation.alert_level !== 'none' && (
              <Alert className={`mt-4 ${
                budgetValidation.alert_level === 'exceeded' ? 'border-red-200 bg-red-50' :
                budgetValidation.alert_level === 'critical' ? 'border-orange-200 bg-orange-50' :
                'border-yellow-200 bg-yellow-50'
              }`}>
                <div className="flex items-start space-x-2">
                  {budgetValidation.alert_level === 'exceeded' ? (
                    <AlertTriangle className="h-4 w-4 text-red-600 mt-0.5" />
                  ) : (
                    <DollarSign className="h-4 w-4 text-orange-600 mt-0.5" />
                  )}
                  <div className="flex-1">
                    <AlertDescription className="text-sm">
                      <strong>Budget Impact:</strong> {budgetValidation.message}
                      {budgetValidation.budget_details && (
                        <div className="mt-1 text-xs text-muted-foreground">
                          Budget: {budgetValidation.budget_details.budget_name}
                          ({budgetValidation.budget_details.account_name})
                        </div>
                      )}
                    </AlertDescription>
                  </div>
                </div>
              </Alert>
            )}
          </div>

          <div className="flex justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={submitting}>
              {submitting ? 'Saving...' : editingPO ? 'Update' : 'Create'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
