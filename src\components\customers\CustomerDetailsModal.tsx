import { useState } from 'react'
import { format } from 'date-fns'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Separator } from '@/components/ui/separator'
import { LoadingSpinner } from '@/components/ui/loading'
import { 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar, 
  FileText, 
  DollarSign,
  Plus,
  Receipt
} from 'lucide-react'
import { useInvoicesByCustomer, usePaymentsByEntity } from '@/hooks/queries'
import { InvoiceDialog } from '@/components/invoices/InvoiceDialog'
import { PaymentForm } from '@/components/payments/PaymentForm'
import type { Customer } from '@/types/database'

interface CustomerDetailsModalProps {
  customer: Customer | null
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function CustomerDetailsModal({ 
  customer, 
  open, 
  onOpenChange 
}: CustomerDetailsModalProps) {
  const [showInvoiceDialog, setShowInvoiceDialog] = useState(false)
  const [showPaymentDialog, setShowPaymentDialog] = useState(false)

  // Fetch customer's invoices and payments
  const { 
    data: invoices = [], 
    isLoading: invoicesLoading 
  } = useInvoicesByCustomer(customer?.id)
  
  const { 
    data: payments = [], 
    isLoading: paymentsLoading 
  } = usePaymentsByEntity('customer', customer?.id)

  if (!customer) return null

  const handleInvoiceSuccess = () => {
    setShowInvoiceDialog(false)
    // React Query will automatically refetch invoices
  }

  const handlePaymentSuccess = () => {
    setShowPaymentDialog(false)
    // React Query will automatically refetch payments
  }

  const formatCurrency = (amount: number) => {
    return `UGX ${amount.toLocaleString()}`
  }

  const getStatusBadge = (status: string) => {
    const statusColors = {
      draft: 'bg-gray-100 text-gray-800',
      sent: 'bg-blue-100 text-blue-800',
      paid: 'bg-green-100 text-green-800',
      overdue: 'bg-red-100 text-red-800',
      cancelled: 'bg-gray-100 text-gray-800',
    }
    
    return (
      <Badge className={statusColors[status as keyof typeof statusColors] || 'bg-gray-100 text-gray-800'}>
        {status}
      </Badge>
    )
  }

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-scroll">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Customer Details - {customer.name}
            </DialogTitle>
          </DialogHeader>
          
          <div className="space-y-6">
            {/* Customer Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  Customer Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Name</label>
                      <p className="font-medium">{customer.name}</p>
                    </div>
                    {customer.email && (
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4 text-gray-400" />
                        <span className="text-sm">{customer.email}</span>
                      </div>
                    )}
                    {customer.phone && (
                      <div className="flex items-center gap-2">
                        <Phone className="h-4 w-4 text-gray-400" />
                        <span className="text-sm">{customer.phone}</span>
                      </div>
                    )}
                  </div>
                  <div className="space-y-3">
                    {customer.address && (
                      <div className="flex items-start gap-2">
                        <MapPin className="h-4 w-4 text-gray-400 mt-0.5" />
                        <span className="text-sm">{customer.address}</span>
                      </div>
                    )}
                    <div>
                      <label className="text-sm font-medium text-gray-500">Payment Terms</label>
                      <p className="text-sm">{customer.payment_terms} days</p>
                    </div>
                    {customer.tin_number && (
                      <div>
                        <label className="text-sm font-medium text-gray-500">TIN Number</label>
                        <p className="text-sm">{customer.tin_number}</p>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Action Buttons */}
            <div className="flex gap-3">
              <Button 
                onClick={() => setShowInvoiceDialog(true)}
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Add Invoice
              </Button>
              <Button 
                variant="outline"
                onClick={() => setShowPaymentDialog(true)}
                className="flex items-center gap-2"
              >
                <DollarSign className="h-4 w-4" />
                Add Payment
              </Button>
            </div>

            <Separator />

            {/* Documents Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Associated Documents</h3>
              
              {/* Invoices */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    Invoices ({invoices.length})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {invoicesLoading ? (
                    <LoadingSpinner size="sm" text="Loading invoices..." />
                  ) : invoices.length > 0 ? (
                    <div className="overflow-x-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Invoice #</TableHead>
                            <TableHead>Date</TableHead>
                            <TableHead>Due Date</TableHead>
                            <TableHead>Amount</TableHead>
                            <TableHead>Status</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {invoices.map((invoice) => (
                            <TableRow key={invoice.id}>
                              <TableCell className="font-medium">
                                {invoice.invoice_number}
                              </TableCell>
                              <TableCell>
                                {format(new Date(invoice.date_issued), 'MMM dd, yyyy')}
                              </TableCell>
                              <TableCell>
                                {format(new Date(invoice.due_date), 'MMM dd, yyyy')}
                              </TableCell>
                              <TableCell>{formatCurrency(invoice.total_amount)}</TableCell>
                              <TableCell>{getStatusBadge(invoice.status)}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  ) : (
                    <p className="text-gray-500 text-center py-4">No invoices found</p>
                  )}
                </CardContent>
              </Card>

              {/* Payments */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Receipt className="h-4 w-4" />
                    Payments ({payments.length})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {paymentsLoading ? (
                    <LoadingSpinner size="sm" text="Loading payments..." />
                  ) : payments.length > 0 ? (
                    <div className="overflow-x-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Payment #</TableHead>
                            <TableHead>Date</TableHead>
                            <TableHead>Amount</TableHead>
                            <TableHead>Method</TableHead>
                            <TableHead>Reference</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {payments.map((payment) => (
                            <TableRow key={payment.id}>
                              <TableCell className="font-medium">
                                {payment.payment_number}
                              </TableCell>
                              <TableCell>
                                {format(new Date(payment.payment_date), 'MMM dd, yyyy')}
                              </TableCell>
                              <TableCell>{formatCurrency(payment.amount)}</TableCell>
                              <TableCell className="capitalize">{payment.payment_method}</TableCell>
                              <TableCell>{payment.reference || '-'}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  ) : (
                    <p className="text-gray-500 text-center py-4">No payments found</p>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Invoice Dialog */}
      <InvoiceDialog
        open={showInvoiceDialog}
        onOpenChange={setShowInvoiceDialog}
        onSuccess={handleInvoiceSuccess}
        preselectedCustomerId={customer.id}
      />

      {/* Payment Dialog */}
      <PaymentForm
        open={showPaymentDialog}
        onOpenChange={setShowPaymentDialog}
        onSuccess={handlePaymentSuccess}
        preselectedPayeeType="customer"
        preselectedPayeeId={customer.id}
      />
    </>
  )
}
