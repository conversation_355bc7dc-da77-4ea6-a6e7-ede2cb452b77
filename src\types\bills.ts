import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Account, WithholdingTaxRate } from './database'

export interface BillFormData {
  vendor_id: string
  account_id: string
  bill_number: string
  date_issued: string
  due_date: string
  withholding_tax_rate_id: string
  notes: string
  status: BillStatus
}

export interface BillLineData {
  account_id?: string
  product_id?: string
  item: string
  description: string
  quantity: number
  unit_price: number
  tax_rate_pct: number
}

export interface BillWithVendor extends Bill {
  vendor: Vendor
}

export interface BillFormProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  editingBill: Bill | null
  vendors: Vendor[]
  accounts: Account[]
  withholdingRates: WithholdingTaxRate[]
  onSubmit: (formData: BillFormData, billLines: BillLineData[]) => Promise<void>
  preselectedVendorId?: string
}

export interface BillListProps {
  bills: BillWithVendor[]
  onEdit: (bill: Bill) => void
  onStatusChange: (bill: Bill, newStatus: BillStatus) => void
  onSubmitForApproval?: (bill: Bill<PERSON>ithVendor) => void
  searchTerm: string
  onSearchChange: (term: string) => void
}

export interface BillLineItemProps {
  line: BillLineData
  index: number
  onUpdate: (index: number, field: keyof BillLineData, value: string | number) => void
  onRemove: (index: number) => void
  isLast: boolean
}
