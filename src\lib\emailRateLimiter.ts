/**
 * Email Rate Limiting Service
 * Prevents email spam and abuse with configurable rate limits
 */

import { supabase } from '@/integrations/supabase/client'
import { getEmailConfiguration } from './emailConfig'
import { logger } from '@/lib/logger'

export interface RateLimitConfig {
  perMinute: number
  perHour: number
  perDay: number
  burstLimit: number
}

export interface RateLimitStatus {
  allowed: boolean
  remaining: {
    perMinute: number
    perHour: number
    perDay: number
    burst: number
  }
  resetTime: {
    perMinute: Date
    perHour: Date
    perDay: Date
    burst: Date
  }
  reason?: string
}

export interface RateLimitViolation {
  identifier: string
  limitType: 'minute' | 'hour' | 'day' | 'burst'
  limit: number
  current: number
  timestamp: Date
  orgId?: string
}

interface RateLimitEntry {
  count: number
  resetTime: number
  window: string
}

/**
 * Email Rate Limiter Class
 */
export class EmailRateLimiter {
  private limits: Map<string, Map<string, RateLimitEntry>> = new Map()
  private config: RateLimitConfig
  private violations: RateLimitViolation[] = []
  private cleanupInterval: NodeJS.Timeout | null = null

  constructor(config?: RateLimitConfig) {
    this.config = config || this.getDefaultConfig()
    this.startCleanup()
  }

  /**
   * Get default rate limit configuration
   */
  private getDefaultConfig(): RateLimitConfig {
    try {
      const emailConfig = getEmailConfiguration()
      return emailConfig.rateLimits
    } catch (error) {
      // Fallback to safe defaults
      return {
        perMinute: 10,
        perHour: 100,
        perDay: 1000,
        burstLimit: 5
      }
    }
  }

  /**
   * Check if email sending is allowed for identifier
   */
  async checkRateLimit(
    identifier: string,
    orgId?: string,
    emailType?: string
  ): Promise<RateLimitStatus> {
    const now = Date.now()
    
    // Get or create rate limit entries for this identifier
    if (!this.limits.has(identifier)) {
      this.limits.set(identifier, new Map())
    }
    
    const userLimits = this.limits.get(identifier)!
    
    // Check each time window
    const minuteStatus = this.checkWindow(userLimits, 'minute', now, this.config.perMinute, 60 * 1000)
    const hourStatus = this.checkWindow(userLimits, 'hour', now, this.config.perHour, 60 * 60 * 1000)
    const dayStatus = this.checkWindow(userLimits, 'day', now, this.config.perDay, 24 * 60 * 60 * 1000)
    const burstStatus = this.checkWindow(userLimits, 'burst', now, this.config.burstLimit, 10 * 1000) // 10 second burst window

    // Determine if request is allowed
    const allowed = minuteStatus.allowed && hourStatus.allowed && dayStatus.allowed && burstStatus.allowed
    
    let reason: string | undefined
    if (!allowed) {
      if (!minuteStatus.allowed) reason = 'Per-minute limit exceeded'
      else if (!hourStatus.allowed) reason = 'Per-hour limit exceeded'
      else if (!dayStatus.allowed) reason = 'Per-day limit exceeded'
      else if (!burstStatus.allowed) reason = 'Burst limit exceeded'
    }

    const status: RateLimitStatus = {
      allowed,
      remaining: {
        perMinute: minuteStatus.remaining,
        perHour: hourStatus.remaining,
        perDay: dayStatus.remaining,
        burst: burstStatus.remaining
      },
      resetTime: {
        perMinute: new Date(minuteStatus.resetTime),
        perHour: new Date(hourStatus.resetTime),
        perDay: new Date(dayStatus.resetTime),
        burst: new Date(burstStatus.resetTime)
      },
      reason
    }

    // Log rate limit violation
    if (!allowed) {
      const violation: RateLimitViolation = {
        identifier,
        limitType: !minuteStatus.allowed ? 'minute' : 
                  !hourStatus.allowed ? 'hour' :
                  !dayStatus.allowed ? 'day' : 'burst',
        limit: !minuteStatus.allowed ? this.config.perMinute :
               !hourStatus.allowed ? this.config.perHour :
               !dayStatus.allowed ? this.config.perDay : this.config.burstLimit,
        current: !minuteStatus.allowed ? minuteStatus.current :
                !hourStatus.allowed ? hourStatus.current :
                !dayStatus.allowed ? dayStatus.current : burstStatus.current,
        timestamp: new Date(),
        orgId
      }

      this.violations.push(violation)
      await this.logViolation(violation, emailType)
    }

    return status
  }

  /**
   * Record email sent (increment counters)
   */
  recordEmailSent(identifier: string): void {
    const now = Date.now()
    
    if (!this.limits.has(identifier)) {
      this.limits.set(identifier, new Map())
    }
    
    const userLimits = this.limits.get(identifier)!
    
    // Increment all windows
    this.incrementWindow(userLimits, 'minute', now, 60 * 1000)
    this.incrementWindow(userLimits, 'hour', now, 60 * 60 * 1000)
    this.incrementWindow(userLimits, 'day', now, 24 * 60 * 60 * 1000)
    this.incrementWindow(userLimits, 'burst', now, 10 * 1000)
  }

  /**
   * Check rate limit for a specific time window
   */
  private checkWindow(
    userLimits: Map<string, RateLimitEntry>,
    window: string,
    now: number,
    limit: number,
    windowMs: number
  ): { allowed: boolean; remaining: number; resetTime: number; current: number } {
    const entry = userLimits.get(window)
    
    if (!entry || now >= entry.resetTime) {
      // Window expired or doesn't exist, allow request
      return {
        allowed: true,
        remaining: limit - 1,
        resetTime: now + windowMs,
        current: 0
      }
    }
    
    const allowed = entry.count < limit
    return {
      allowed,
      remaining: Math.max(0, limit - entry.count - 1),
      resetTime: entry.resetTime,
      current: entry.count
    }
  }

  /**
   * Increment counter for a specific time window
   */
  private incrementWindow(
    userLimits: Map<string, RateLimitEntry>,
    window: string,
    now: number,
    windowMs: number
  ): void {
    const entry = userLimits.get(window)
    
    if (!entry || now >= entry.resetTime) {
      // Create new window
      userLimits.set(window, {
        count: 1,
        resetTime: now + windowMs,
        window
      })
    } else {
      // Increment existing window
      entry.count++
    }
  }

  /**
   * Log rate limit violation
   */
  private async logViolation(violation: RateLimitViolation, emailType?: string): Promise<void> {
    try {
      logger.warn('Email rate limit violation', {
        component: 'EmailRateLimiter',
        action: 'checkRateLimit',
        metadata: {
          identifier: violation.identifier,
          limitType: violation.limitType,
          limit: violation.limit,
          current: violation.current,
          orgId: violation.orgId,
          emailType
        }
      })

      // Store violation in database for monitoring
      await supabase
        .from('email_rate_limit_violations')
        .insert({
          identifier: violation.identifier,
          limit_type: violation.limitType,
          limit_value: violation.limit,
          current_count: violation.current,
          org_id: violation.orgId,
          email_type: emailType,
          violated_at: violation.timestamp.toISOString()
        })
        .catch(error => {
          // Don't fail if logging fails
          console.error('Failed to log rate limit violation:', error)
        })

    } catch (error) {
      console.error('Error logging rate limit violation:', error)
    }
  }

  /**
   * Get rate limit statistics
   */
  async getRateLimitStats(orgId?: string): Promise<{
    violations: number
    topViolators: Array<{ identifier: string; count: number }>
    violationsByType: Record<string, number>
    recentViolations: RateLimitViolation[]
  }> {
    try {
      let query = supabase
        .from('email_rate_limit_violations')
        .select('*')
        .gte('violated_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()) // Last 24 hours

      if (orgId) {
        query = query.eq('org_id', orgId)
      }

      const { data: violations, error } = await query

      if (error) {
        throw error
      }

      const stats = {
        violations: violations?.length || 0,
        topViolators: this.getTopViolators(violations || []),
        violationsByType: this.getViolationsByType(violations || []),
        recentViolations: this.violations.slice(-10) // Last 10 in-memory violations
      }

      return stats
    } catch (error) {
      logger.error('Error getting rate limit stats', {
        component: 'EmailRateLimiter',
        action: 'getRateLimitStats',
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      
      return {
        violations: 0,
        topViolators: [],
        violationsByType: {},
        recentViolations: []
      }
    }
  }

  /**
   * Get top violators from violations data
   */
  private getTopViolators(violations: any[]): Array<{ identifier: string; count: number }> {
    const violatorCounts = violations.reduce((acc, violation) => {
      acc[violation.identifier] = (acc[violation.identifier] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    return Object.entries(violatorCounts)
      .map(([identifier, count]) => ({ identifier, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10)
  }

  /**
   * Get violations by type
   */
  private getViolationsByType(violations: any[]): Record<string, number> {
    return violations.reduce((acc, violation) => {
      acc[violation.limit_type] = (acc[violation.limit_type] || 0) + 1
      return acc
    }, {} as Record<string, number>)
  }

  /**
   * Reset rate limits for identifier
   */
  resetLimits(identifier: string): void {
    this.limits.delete(identifier)
    
    logger.info('Rate limits reset', {
      component: 'EmailRateLimiter',
      action: 'resetLimits',
      metadata: { identifier }
    })
  }

  /**
   * Update rate limit configuration
   */
  updateConfig(config: Partial<RateLimitConfig>): void {
    this.config = { ...this.config, ...config }
    
    logger.info('Rate limit configuration updated', {
      component: 'EmailRateLimiter',
      action: 'updateConfig',
      metadata: { config: this.config }
    })
  }

  /**
   * Start cleanup interval
   */
  private startCleanup(): void {
    this.cleanupInterval = setInterval(() => {
      this.cleanup()
    }, 5 * 60 * 1000) // Cleanup every 5 minutes
  }

  /**
   * Cleanup expired entries
   */
  private cleanup(): void {
    const now = Date.now()
    let cleanedCount = 0

    for (const [identifier, userLimits] of this.limits.entries()) {
      for (const [window, entry] of userLimits.entries()) {
        if (now >= entry.resetTime) {
          userLimits.delete(window)
          cleanedCount++
        }
      }
      
      if (userLimits.size === 0) {
        this.limits.delete(identifier)
      }
    }

    // Clean old violations (keep last 1000)
    if (this.violations.length > 1000) {
      this.violations = this.violations.slice(-1000)
    }

    if (cleanedCount > 0) {
      logger.debug('Rate limiter cleanup completed', {
        component: 'EmailRateLimiter',
        action: 'cleanup',
        metadata: { cleanedCount }
      })
    }
  }

  /**
   * Stop cleanup interval
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
      this.cleanupInterval = null
    }
  }
}

// Export singleton instance
export const emailRateLimiter = new EmailRateLimiter()
