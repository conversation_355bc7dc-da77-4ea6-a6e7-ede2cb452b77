import { z } from 'zod'

// Form validation schema
export const productFormSchema = z.object({
  sku: z.string().min(1, 'SKU is required'),
  name: z.string().min(1, 'Product name is required'),
  description: z.string().optional(),
  category_id: z.string().nullable().optional(),
  unit_of_measure: z.string().min(1, 'Unit of measure is required'),
  cost_price: z.number().min(0, 'Cost price must be positive').default(0),
  selling_price: z.number().min(0, 'Selling price must be positive').default(0),
  track_inventory: z.boolean().default(true),
  initial_quantity: z.number().min(0, 'Initial quantity must be positive').default(0),
  reorder_level: z.number().min(0, 'Reorder level must be positive').default(0),
  reorder_quantity: z.number().min(0, 'Reorder quantity must be positive').default(0),
  barcode: z.string().optional(),
  weight: z.number().nullable().optional(),
  dimensions: z.string().optional(),
  is_active: z.boolean().default(true),
  is_sellable: z.boolean().default(true),
  is_purchasable: z.boolean().default(true),
})

export type ProductFormValues = z.infer<typeof productFormSchema>

// Validation and warning types
export interface ValidationResult {
  errors: string[]
  warnings: string[]
}

export interface PricingValidation extends ValidationResult {
  marginPercentage: number
  markupPercentage: number
}

// SKU generation options
export interface SkuGenerationOptions {
  productName: string
  categoryId?: string
  includeDate?: boolean
  includeRandom?: boolean
  length?: number
}
