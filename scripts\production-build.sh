#!/bin/bash

# Kaya Finance - Production Build Script
# This script prepares and builds the application for production deployment

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BUILD_DIR="dist"
BACKUP_DIR="dist-backup"
LOG_FILE="build-$(date +%Y%m%d-%H%M%S).log"

echo -e "${BLUE}🚀 Starting Kaya Finance Production Build${NC}"
echo "Build started at: $(date)"
echo "Logging to: $LOG_FILE"

# Function to log messages
log() {
    echo -e "$1" | tee -a "$LOG_FILE"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Pre-build checks
log "${YELLOW}📋 Running pre-build checks...${NC}"

# Check Node.js version
if ! command_exists node; then
    log "${RED}❌ Node.js is not installed${NC}"
    exit 1
fi

NODE_VERSION=$(node --version)
log "✅ Node.js version: $NODE_VERSION"

# Check npm version
if ! command_exists npm; then
    log "${RED}❌ npm is not installed${NC}"
    exit 1
fi

NPM_VERSION=$(npm --version)
log "✅ npm version: $NPM_VERSION"

# Check if package.json exists
if [ ! -f "package.json" ]; then
    log "${RED}❌ package.json not found${NC}"
    exit 1
fi

# Check if production environment file exists
if [ ! -f ".env.production" ]; then
    log "${YELLOW}⚠️  .env.production not found, using .env.example as template${NC}"
    if [ -f ".env.example" ]; then
        cp .env.example .env.production
        log "${YELLOW}📝 Please update .env.production with actual production values${NC}"
    else
        log "${RED}❌ No environment configuration found${NC}"
        exit 1
    fi
fi

# Backup existing build if it exists
if [ -d "$BUILD_DIR" ]; then
    log "${YELLOW}📦 Backing up existing build...${NC}"
    rm -rf "$BACKUP_DIR"
    mv "$BUILD_DIR" "$BACKUP_DIR"
fi

# Install dependencies
log "${YELLOW}📦 Installing dependencies...${NC}"
npm ci --production=false 2>&1 | tee -a "$LOG_FILE"

# Run security audit
log "${YELLOW}🔒 Running security audit...${NC}"
npm audit --audit-level=high 2>&1 | tee -a "$LOG_FILE" || {
    log "${YELLOW}⚠️  Security vulnerabilities found. Consider running 'npm audit fix'${NC}"
}

# Run linting
log "${YELLOW}🔍 Running linting...${NC}"
npm run lint 2>&1 | tee -a "$LOG_FILE" || {
    log "${RED}❌ Linting failed${NC}"
    exit 1
}

# Run type checking
log "${YELLOW}🔍 Running type checking...${NC}"
npm run type-check 2>&1 | tee -a "$LOG_FILE" || {
    log "${RED}❌ Type checking failed${NC}"
    exit 1
}

# Run tests
log "${YELLOW}🧪 Running tests...${NC}"
npm run test 2>&1 | tee -a "$LOG_FILE" || {
    log "${RED}❌ Tests failed${NC}"
    exit 1
}

# Build for production
log "${YELLOW}🏗️  Building for production...${NC}"
npm run build:production 2>&1 | tee -a "$LOG_FILE" || {
    log "${RED}❌ Production build failed${NC}"
    
    # Restore backup if build failed
    if [ -d "$BACKUP_DIR" ]; then
        log "${YELLOW}🔄 Restoring previous build...${NC}"
        rm -rf "$BUILD_DIR"
        mv "$BACKUP_DIR" "$BUILD_DIR"
    fi
    
    exit 1
}

# Verify build output
log "${YELLOW}✅ Verifying build output...${NC}"

if [ ! -d "$BUILD_DIR" ]; then
    log "${RED}❌ Build directory not found${NC}"
    exit 1
fi

if [ ! -f "$BUILD_DIR/index.html" ]; then
    log "${RED}❌ index.html not found in build directory${NC}"
    exit 1
fi

# Check build size
BUILD_SIZE=$(du -sh "$BUILD_DIR" | cut -f1)
log "📊 Build size: $BUILD_SIZE"

# Generate build report
log "${YELLOW}📊 Generating build report...${NC}"
cat > "$BUILD_DIR/build-info.json" << EOF
{
  "buildDate": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "buildSize": "$BUILD_SIZE",
  "nodeVersion": "$NODE_VERSION",
  "npmVersion": "$NPM_VERSION",
  "gitCommit": "$(git rev-parse HEAD 2>/dev/null || echo 'unknown')",
  "gitBranch": "$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo 'unknown')",
  "environment": "production"
}
EOF

# Run bundle analysis (optional)
if command_exists npx; then
    log "${YELLOW}📈 Running bundle analysis...${NC}"
    npm run analyze 2>&1 | tee -a "$LOG_FILE" || {
        log "${YELLOW}⚠️  Bundle analysis failed or not configured${NC}"
    }
fi

# Security headers check
log "${YELLOW}🔒 Checking security headers...${NC}"
if [ -f "$BUILD_DIR/index.html" ]; then
    # Check for basic security measures in HTML
    if grep -q "Content-Security-Policy" "$BUILD_DIR/index.html"; then
        log "✅ CSP headers found"
    else
        log "${YELLOW}⚠️  Consider adding Content-Security-Policy headers${NC}"
    fi
fi

# Clean up backup if build was successful
if [ -d "$BACKUP_DIR" ]; then
    log "${GREEN}🗑️  Cleaning up backup...${NC}"
    rm -rf "$BACKUP_DIR"
fi

# Final checks
log "${YELLOW}🔍 Running final checks...${NC}"

# Check for common issues
if grep -r "console.log" "$BUILD_DIR" >/dev/null 2>&1; then
    log "${YELLOW}⚠️  console.log statements found in production build${NC}"
fi

if grep -r "debugger" "$BUILD_DIR" >/dev/null 2>&1; then
    log "${YELLOW}⚠️  debugger statements found in production build${NC}"
fi

# Success message
log "${GREEN}✅ Production build completed successfully!${NC}"
log "${GREEN}📁 Build output: $BUILD_DIR${NC}"
log "${GREEN}📊 Build size: $BUILD_SIZE${NC}"
log "Build completed at: $(date)"

echo ""
echo -e "${GREEN}🎉 Ready for deployment!${NC}"
echo ""
echo "Next steps:"
echo "1. Review the build output in the '$BUILD_DIR' directory"
echo "2. Test the production build locally with: npm run preview:prod"
echo "3. Deploy to your hosting platform"
echo "4. Run post-deployment tests"
echo ""
echo "Build log saved to: $LOG_FILE"
