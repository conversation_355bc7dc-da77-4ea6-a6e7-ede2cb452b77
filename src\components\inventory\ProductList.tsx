import React, { useState, useMemo } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Search,
  Plus,
  MoreHorizontal,
  Edit,
  Eye,
  Trash2,
  Package,
  AlertTriangle,
  CheckCircle,
  XCircle,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  Filter,
  Download,
  Copy
} from 'lucide-react'
import {
  useProductsWithStock,
  useToggleProductStatus,
  useDeleteProduct,
  useActiveProductCategories,
  useBulkProductOperations,
  useDuplicateProduct
} from '@/hooks/queries'
import { ProductQuickActions } from './ProductQuickActions'
import { useDebounce } from '@/hooks/use-debounce'
import type { ProductWithStock, ProductFilters } from '@/types/inventory'

type SortField = 'name' | 'sku' | 'category' | 'stock_status' | 'quantity' | 'price' | 'status'
type SortDirection = 'asc' | 'desc'

interface ProductListProps {
  onCreateProduct?: () => void
  onEditProduct: (product: ProductWithStock) => void
  onViewProduct: (product: ProductWithStock) => void
  hideCreateButton?: boolean
}

export function ProductList({ onCreateProduct, onEditProduct, onViewProduct, hideCreateButton = false }: ProductListProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [filters, setFilters] = useState<ProductFilters>({})
  const [sortField, setSortField] = useState<SortField>('name')
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc')
  const [selectedProducts, setSelectedProducts] = useState<string[]>([])
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(25)
  const [showFilters, setShowFilters] = useState(false)

  const debouncedSearch = useDebounce(searchTerm, 300)

  const { data: products = [], isLoading, error } = useProductsWithStock()
  const { data: categories = [] } = useActiveProductCategories()
  const toggleStatus = useToggleProductStatus()
  const deleteProduct = useDeleteProduct()
  const bulkOperations = useBulkProductOperations()
  const duplicateProduct = useDuplicateProduct()

  // Filter, sort, and paginate products
  const processedProducts = useMemo(() => {
    // Filter products
    let filtered = products.filter(product => {
      if (debouncedSearch) {
        const searchLower = debouncedSearch.toLowerCase()
        const matchesSearch =
          product.name.toLowerCase().includes(searchLower) ||
          product.sku.toLowerCase().includes(searchLower) ||
          (product.description && product.description.toLowerCase().includes(searchLower)) ||
          (product.barcode && product.barcode.toLowerCase().includes(searchLower))

        if (!matchesSearch) return false
      }

      if (filters.category_id && product.category_id !== filters.category_id) {
        return false
      }

      if (filters.is_active !== undefined && product.is_active !== filters.is_active) {
        return false
      }

      if (filters.is_sellable !== undefined && product.is_sellable !== filters.is_sellable) {
        return false
      }

      if (filters.track_inventory !== undefined && product.track_inventory !== filters.track_inventory) {
        return false
      }

      if (filters.low_stock && !product.is_low_stock) {
        return false
      }

      if (filters.out_of_stock && (product.total_quantity_available || 0) > 0) {
        return false
      }

      return true
    })

    // Sort products
    filtered.sort((a, b) => {
      let aValue: any, bValue: any

      switch (sortField) {
        case 'name':
          aValue = a.name.toLowerCase()
          bValue = b.name.toLowerCase()
          break
        case 'sku':
          aValue = a.sku.toLowerCase()
          bValue = b.sku.toLowerCase()
          break
        case 'category':
          aValue = a.category?.name?.toLowerCase() || 'zzz'
          bValue = b.category?.name?.toLowerCase() || 'zzz'
          break
        case 'stock_status':
          aValue = a.is_low_stock ? 2 : (a.total_quantity_available || 0) <= 0 ? 3 : 1
          bValue = b.is_low_stock ? 2 : (b.total_quantity_available || 0) <= 0 ? 3 : 1
          break
        case 'quantity':
          aValue = a.total_quantity_available || 0
          bValue = b.total_quantity_available || 0
          break
        case 'price':
          aValue = a.selling_price || 0
          bValue = b.selling_price || 0
          break
        case 'status':
          aValue = a.is_active ? 1 : 0
          bValue = b.is_active ? 1 : 0
          break
        default:
          aValue = a.name.toLowerCase()
          bValue = b.name.toLowerCase()
      }

      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1
      return 0
    })

    // Paginate
    const startIndex = (currentPage - 1) * pageSize
    const endIndex = startIndex + pageSize
    const paginated = filtered.slice(startIndex, endIndex)

    return {
      filtered,
      paginated,
      totalCount: filtered.length,
      totalPages: Math.ceil(filtered.length / pageSize)
    }
  }, [products, debouncedSearch, filters, sortField, sortDirection, currentPage, pageSize])

  const { filtered: filteredProducts, paginated: paginatedProducts, totalCount, totalPages } = processedProducts

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('asc')
    }
    setCurrentPage(1)
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedProducts(paginatedProducts.map(p => p.id))
    } else {
      setSelectedProducts([])
    }
  }

  const handleSelectProduct = (productId: string, checked: boolean) => {
    if (checked) {
      setSelectedProducts(prev => [...prev, productId])
    } else {
      setSelectedProducts(prev => prev.filter(id => id !== productId))
    }
  }

  const handleBulkAction = async (action: 'activate' | 'deactivate' | 'delete') => {
    if (selectedProducts.length === 0) return

    const confirmMessage = action === 'delete'
      ? `Are you sure you want to delete ${selectedProducts.length} products? This action cannot be undone.`
      : `Are you sure you want to ${action} ${selectedProducts.length} products?`

    if (!window.confirm(confirmMessage)) return

    try {
      if (action === 'delete') {
        await Promise.all(selectedProducts.map(id => deleteProduct.mutateAsync(id)))
      } else {
        await bulkOperations.mutateAsync({
          productIds: selectedProducts,
          operation: action,
        })
      }
      setSelectedProducts([])
    } catch (error) {
      console.error(`Failed to ${action} products:`, error)
    }
  }

  const handleToggleStatus = async (product: ProductWithStock) => {
    try {
      await toggleStatus.mutateAsync({
        productId: product.id,
        isActive: !product.is_active
      })
    } catch (error) {
      console.error('Failed to toggle product status:', error)
    }
  }

  const handleDeleteProduct = async (product: ProductWithStock) => {
    if (window.confirm(`Are you sure you want to delete "${product.name}"? This action cannot be undone.`)) {
      try {
        await deleteProduct.mutateAsync(product.id)
      } catch (error) {
        console.error('Failed to delete product:', error)
      }
    }
  }

  const handleDuplicateProduct = async (product: ProductWithStock) => {
    try {
      await duplicateProduct.mutateAsync(product.id)
    } catch (error) {
      console.error('Failed to duplicate product:', error)
    }
  }

  const handleExportProducts = () => {
    const csvContent = [
      ['Name', 'SKU', 'Category', 'Description', 'Unit Price', 'Cost Price', 'Stock On Hand', 'Stock Available', 'Status'],
      ...filteredProducts.map(product => [
        product.name,
        product.sku,
        product.category?.name || '',
        product.description || '',
        product.selling_price?.toString() || '0',
        product.cost_price?.toString() || '0',
        product.total_quantity_on_hand?.toString() || '0',
        product.total_quantity_available?.toString() || '0',
        product.is_active ? 'Active' : 'Inactive'
      ])
    ].map(row => row.map(cell => `"${cell}"`).join(',')).join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `products-${new Date().toISOString().split('T')[0]}.csv`
    a.click()
    URL.revokeObjectURL(url)
  }

  const getStockStatusBadge = (product: ProductWithStock) => {
    if (!product.track_inventory) {
      return <Badge variant="secondary">Not Tracked</Badge>
    }

    const available = product.total_quantity_available || 0
    
    if (available <= 0) {
      return <Badge variant="destructive">Out of Stock</Badge>
    }
    
    if (product.is_low_stock) {
      return <Badge variant="outline" className="border-orange-500 text-orange-600">Low Stock</Badge>
    }
    
    return <Badge variant="default" className="bg-green-100 text-green-800">In Stock</Badge>
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-red-600">
            Failed to load products. Please try again.
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Products
              <Badge variant="secondary" className="ml-2">
                {totalCount}
              </Badge>
            </CardTitle>
            <CardDescription>
              Manage your product catalog and inventory
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            {selectedProducts.length > 0 && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    Bulk Actions ({selectedProducts.length})
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem onClick={() => handleBulkAction('activate')}>
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Activate Selected
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleBulkAction('deactivate')}>
                    <XCircle className="mr-2 h-4 w-4" />
                    Deactivate Selected
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onClick={() => handleBulkAction('delete')}
                    className="text-red-600"
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete Selected
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}
            <Button variant="outline" size="sm" onClick={handleExportProducts}>
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            {!hideCreateButton && onCreateProduct && (
              <Button onClick={onCreateProduct}>
                <Plus className="h-4 w-4 mr-2" />
                Add Product
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Quick Actions for Selected Products */}
          <ProductQuickActions
            selectedProducts={paginatedProducts.filter(p => selectedProducts.includes(p.id))}
            onSelectionChange={(products) => setSelectedProducts(products.map(p => p.id))}
            onProductUpdate={() => {
              // Refresh will happen automatically via React Query
              setSelectedProducts([])
            }}
          />

          {/* Search and Filters */}
          <div className="space-y-4">
            <div className="flex items-center gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search products by name, SKU, barcode, or description..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
              >
                <Filter className="h-4 w-4 mr-2" />
                Filters
              </Button>
            </div>

            {showFilters && (
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4 border rounded-lg bg-gray-50">
                <div>
                  <label className="text-sm font-medium mb-2 block">Category</label>
                  <Select
                    value={filters.category_id || 'all'}
                    onValueChange={(value) => setFilters(prev => ({
                      ...prev,
                      category_id: value === 'all' ? undefined : value
                    }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All Categories" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Categories</SelectItem>
                      {categories.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">Status</label>
                  <Select
                    value={filters.is_active === undefined ? 'all' : filters.is_active ? 'active' : 'inactive'}
                    onValueChange={(value) => setFilters(prev => ({
                      ...prev,
                      is_active: value === 'all' ? undefined : value === 'active'
                    }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="active">Active Only</SelectItem>
                      <SelectItem value="inactive">Inactive Only</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">Stock Status</label>
                  <Select
                    value={filters.low_stock ? 'low' : filters.out_of_stock ? 'out' : 'all'}
                    onValueChange={(value) => setFilters(prev => ({
                      ...prev,
                      low_stock: value === 'low',
                      out_of_stock: value === 'out'
                    }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All Stock" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Stock</SelectItem>
                      <SelectItem value="low">Low Stock</SelectItem>
                      <SelectItem value="out">Out of Stock</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">Sellable</label>
                  <Select
                    value={filters.is_sellable === undefined ? 'all' : filters.is_sellable ? 'yes' : 'no'}
                    onValueChange={(value) => setFilters(prev => ({
                      ...prev,
                      is_sellable: value === 'all' ? undefined : value === 'yes'
                    }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All Products" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Products</SelectItem>
                      <SelectItem value="yes">Sellable Only</SelectItem>
                      <SelectItem value="no">Non-Sellable</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            )}
          </div>

          {/* Products Table */}
          <div className="border rounded-lg">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[50px]">
                    <Checkbox
                      checked={selectedProducts.length === paginatedProducts.length && paginatedProducts.length > 0}
                      onCheckedChange={handleSelectAll}
                    />
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-gray-50"
                    onClick={() => handleSort('name')}
                  >
                    <div className="flex items-center gap-1">
                      Product
                      {sortField === 'name' && (
                        sortDirection === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />
                      )}
                    </div>
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-gray-50"
                    onClick={() => handleSort('sku')}
                  >
                    <div className="flex items-center gap-1">
                      SKU
                      {sortField === 'sku' && (
                        sortDirection === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />
                      )}
                    </div>
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-gray-50"
                    onClick={() => handleSort('category')}
                  >
                    <div className="flex items-center gap-1">
                      Category
                      {sortField === 'category' && (
                        sortDirection === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />
                      )}
                    </div>
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-gray-50"
                    onClick={() => handleSort('stock_status')}
                  >
                    <div className="flex items-center gap-1">
                      Stock Status
                      {sortField === 'stock_status' && (
                        sortDirection === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />
                      )}
                    </div>
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-gray-50"
                    onClick={() => handleSort('quantity')}
                  >
                    <div className="flex items-center gap-1">
                      Available
                      {sortField === 'quantity' && (
                        sortDirection === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />
                      )}
                    </div>
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-gray-50"
                    onClick={() => handleSort('price')}
                  >
                    <div className="flex items-center gap-1">
                      Unit Price
                      {sortField === 'price' && (
                        sortDirection === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />
                      )}
                    </div>
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-gray-50"
                    onClick={() => handleSort('status')}
                  >
                    <div className="flex items-center gap-1">
                      Status
                      {sortField === 'status' && (
                        sortDirection === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />
                      )}
                    </div>
                  </TableHead>
                  <TableHead className="w-[50px]"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={10} className="text-center py-8">
                      Loading products...
                    </TableCell>
                  </TableRow>
                ) : paginatedProducts.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={10} className="text-center py-8">
                      {debouncedSearch || Object.keys(filters).some(key => filters[key as keyof ProductFilters] !== undefined)
                        ? 'No products match your search criteria.'
                        : 'No products found. Create your first product to get started.'
                      }
                    </TableCell>
                  </TableRow>
                ) : (
                  paginatedProducts.map((product) => (
                    <TableRow key={product.id} className="hover:bg-gray-50">
                      <TableCell>
                        <Checkbox
                          checked={selectedProducts.includes(product.id)}
                          onCheckedChange={(checked) => handleSelectProduct(product.id, checked as boolean)}
                        />
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{product.name}</div>
                          {product.description && (
                            <div className="text-sm text-gray-500 truncate max-w-[200px]">
                              {product.description}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="font-mono text-sm">{product.sku}</TableCell>
                      <TableCell>
                        {product.category?.name || (
                          <span className="text-gray-400">Uncategorized</span>
                        )}
                      </TableCell>
                      <TableCell>{getStockStatusBadge(product)}</TableCell>
                      <TableCell>
                        {product.track_inventory ? (
                          <span className="font-mono">
                            {product.total_quantity_available || 0} {product.unit_of_measure}
                          </span>
                        ) : (
                          <span className="text-gray-400">-</span>
                        )}
                      </TableCell>
                      <TableCell>
                        UGX {product.selling_price?.toLocaleString() || '0'}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          {product.is_active ? (
                            <CheckCircle className="h-4 w-4 text-green-600" />
                          ) : (
                            <XCircle className="h-4 w-4 text-red-600" />
                          )}
                          <span className={product.is_active ? 'text-green-600' : 'text-red-600'}>
                            {product.is_active ? 'Active' : 'Inactive'}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => onViewProduct(product)}>
                              <Eye className="mr-2 h-4 w-4" />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => onEditProduct(product)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit Product
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleDuplicateProduct(product)}>
                              <Copy className="mr-2 h-4 w-4" />
                              Duplicate
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => handleToggleStatus(product)}>
                              {product.is_active ? (
                                <>
                                  <XCircle className="mr-2 h-4 w-4" />
                                  Deactivate
                                </>
                              ) : (
                                <>
                                  <CheckCircle className="mr-2 h-4 w-4" />
                                  Activate
                                </>
                              )}
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => handleDeleteProduct(product)}
                              className="text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination and Summary */}
          {totalCount > 0 && (
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-500">
                Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, totalCount)} of {totalCount} products
                {totalCount !== products.length && (
                  <span className="ml-1">({products.length} total)</span>
                )}
              </div>

              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-500">Rows per page:</span>
                  <Select
                    value={pageSize.toString()}
                    onValueChange={(value) => {
                      setPageSize(Number(value))
                      setCurrentPage(1)
                    }}
                  >
                    <SelectTrigger className="w-20">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="10">10</SelectItem>
                      <SelectItem value="25">25</SelectItem>
                      <SelectItem value="50">50</SelectItem>
                      <SelectItem value="100">100</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {totalPages > 1 && (
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      disabled={currentPage === 1}
                    >
                      Previous
                    </Button>

                    <div className="flex items-center gap-1">
                      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                        let pageNum
                        if (totalPages <= 5) {
                          pageNum = i + 1
                        } else if (currentPage <= 3) {
                          pageNum = i + 1
                        } else if (currentPage >= totalPages - 2) {
                          pageNum = totalPages - 4 + i
                        } else {
                          pageNum = currentPage - 2 + i
                        }

                        return (
                          <Button
                            key={pageNum}
                            variant={currentPage === pageNum ? "default" : "outline"}
                            size="sm"
                            className="w-8 h-8 p-0"
                            onClick={() => setCurrentPage(pageNum)}
                          >
                            {pageNum}
                          </Button>
                        )
                      })}
                    </div>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                      disabled={currentPage === totalPages}
                    >
                      Next
                    </Button>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
