import React from 'react'
import { UseFormReturn } from 'react-hook-form'
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { ProductFormValues } from './types'

interface ProductPhysicalProps {
  form: UseFormReturn<ProductFormValues>
}

export function ProductPhysical({ form }: ProductPhysicalProps) {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Physical Properties</h3>
      
      <div className="grid grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="weight"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Weight (kg)</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  step="0.001"
                  min="0"
                  placeholder="0.000"
                  {...field}
                  value={field.value || ''}
                  onChange={(e) => {
                    const value = e.target.value
                    field.onChange(value === '' ? null : parseFloat(value) || 0)
                  }}
                />
              </FormControl>
              <FormDescription>
                Product weight in kilograms
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="dimensions"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Dimensions</FormLabel>
              <FormControl>
                <Input
                  placeholder="L x W x H (e.g., 10 x 5 x 3 cm)"
                  {...field}
                />
              </FormControl>
              <FormDescription>
                Product dimensions (optional)
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  )
}
